--[[
	Animation System
	Enhanced animations using TweenService for smooth UI and building effects
]]

local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local AnimationSystem = {}

-- Animation presets
AnimationSystem.PRESETS = {
	-- UI Animations
	SLIDE_IN = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
	SLIDE_OUT = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
	FADE_IN = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
	FADE_OUT = TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
	BOUNCE = TweenInfo.new(0.4, Enum.EasingStyle.Elastic, Enum.EasingDirection.Out),
	SMOOTH = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut),
	
	-- Building Animations
	BUILD_RISE = TweenInfo.new(1.0, Enum.EasingStyle.Bounce, Enum.EasingDirection.Out),
	BUILD_SCALE = TweenInfo.new(0.8, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
	
	-- Effect Animations
	GLOW_PULSE = TweenInfo.new(1.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
	ROTATE_SLOW = TweenInfo.new(10, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1),
	FLOAT = TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
}

-- Active animations tracking
local activeAnimations = {}

-- Animate UI element slide in
function AnimationSystem.SlideIn(element, direction, callback)
	direction = direction or "Bottom"
	
	local startPos = element.Position
	local targetPos = startPos
	
	-- Set starting position based on direction
	if direction == "Bottom" then
		element.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset, 1, 0)
	elseif direction == "Top" then
		element.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset, -1, 0)
	elseif direction == "Left" then
		element.Position = UDim2.new(-1, 0, startPos.Y.Scale, startPos.Y.Offset)
	elseif direction == "Right" then
		element.Position = UDim2.new(1, 0, startPos.Y.Scale, startPos.Y.Offset)
	end
	
	element.Visible = true
	
	local tween = TweenService:Create(element, AnimationSystem.PRESETS.SLIDE_IN, {
		Position = targetPos
	})
	
	if callback then
		tween.Completed:Connect(callback)
	end
	
	tween:Play()
	return tween
end

-- Animate UI element slide out
function AnimationSystem.SlideOut(element, direction, callback)
	direction = direction or "Bottom"
	
	local currentPos = element.Position
	local targetPos
	
	-- Set target position based on direction
	if direction == "Bottom" then
		targetPos = UDim2.new(currentPos.X.Scale, currentPos.X.Offset, 1, 0)
	elseif direction == "Top" then
		targetPos = UDim2.new(currentPos.X.Scale, currentPos.X.Offset, -1, 0)
	elseif direction == "Left" then
		targetPos = UDim2.new(-1, 0, currentPos.Y.Scale, currentPos.Y.Offset)
	elseif direction == "Right" then
		targetPos = UDim2.new(1, 0, currentPos.Y.Scale, currentPos.Y.Offset)
	end
	
	local tween = TweenService:Create(element, AnimationSystem.PRESETS.SLIDE_OUT, {
		Position = targetPos
	})
	
	tween.Completed:Connect(function()
		element.Visible = false
		if callback then callback() end
	end)
	
	tween:Play()
	return tween
end

-- Animate building placement
function AnimationSystem.AnimateBuildingPlacement(buildingModel, callback)
	if not buildingModel or not buildingModel.PrimaryPart then return end
	
	local originalSize = buildingModel.PrimaryPart.Size
	local originalPosition = buildingModel.PrimaryPart.Position
	
	-- Start small and underground
	buildingModel.PrimaryPart.Size = Vector3.new(0.1, 0.1, 0.1)
	buildingModel.PrimaryPart.Position = originalPosition - Vector3.new(0, originalSize.Y, 0)
	
	-- Create particle effect
	AnimationSystem.CreateBuildingParticles(buildingModel.PrimaryPart)
	
	-- Animate size and position
	local sizeTween = TweenService:Create(buildingModel.PrimaryPart, AnimationSystem.PRESETS.BUILD_SCALE, {
		Size = originalSize
	})
	
	local positionTween = TweenService:Create(buildingModel.PrimaryPart, AnimationSystem.PRESETS.BUILD_RISE, {
		Position = originalPosition
	})
	
	sizeTween:Play()
	positionTween:Play()
	
	positionTween.Completed:Connect(function()
		if callback then callback() end
	end)
	
	return {sizeTween, positionTween}
end

-- Create building placement particles
function AnimationSystem.CreateBuildingParticles(part)
	local attachment = Instance.new("Attachment")
	attachment.Parent = part
	
	-- Dust particles
	local particles = Instance.new("ParticleEmitter")
	particles.Parent = attachment
	particles.Texture = "rbxasset://textures/particles/smoke_main.dds"
	particles.Lifetime = NumberRange.new(0.5, 1.5)
	particles.Rate = 50
	particles.SpreadAngle = Vector2.new(45, 45)
	particles.Speed = NumberRange.new(5, 15)
	particles.Color = ColorSequence.new(Color3.new(0.8, 0.6, 0.4))
	particles.Size = NumberSequence.new{
		NumberSequenceKeypoint.new(0, 0.5),
		NumberSequenceKeypoint.new(0.5, 1),
		NumberSequenceKeypoint.new(1, 0)
	}
	
	-- Auto-cleanup after 2 seconds
	task.delay(2, function()
		particles.Enabled = false
		task.wait(2)
		attachment:Destroy()
	end)
end

-- Animate currency gain
function AnimationSystem.AnimateCurrencyGain(currencyFrame, amount)
	-- Create floating text
	local floatingText = Instance.new("TextLabel")
	floatingText.Size = UDim2.new(0, 100, 0, 30)
	floatingText.Position = UDim2.new(0.5, -50, 0.5, -15)
	floatingText.BackgroundTransparency = 1
	floatingText.Text = "+" .. amount
	floatingText.TextColor3 = Color3.new(0.2, 1, 0.2)
	floatingText.TextScaled = true
	floatingText.Font = Enum.Font.SourceSansBold
	floatingText.Parent = currencyFrame
	
	-- Animate floating up and fading
	local moveTween = TweenService:Create(floatingText, AnimationSystem.PRESETS.SMOOTH, {
		Position = UDim2.new(0.5, -50, 0, -30),
		TextTransparency = 1
	})
	
	moveTween.Completed:Connect(function()
		floatingText:Destroy()
	end)
	
	moveTween:Play()
	
	-- Animate currency frame glow
	local originalColor = currencyFrame.BackgroundColor3
	local glowTween = TweenService:Create(currencyFrame, AnimationSystem.PRESETS.BOUNCE, {
		BackgroundColor3 = Color3.new(0.3, 1, 0.3)
	})
	
	glowTween.Completed:Connect(function()
		TweenService:Create(currencyFrame, AnimationSystem.PRESETS.SMOOTH, {
			BackgroundColor3 = originalColor
		}):Play()
	end)
	
	glowTween:Play()
end

-- Animate button press
function AnimationSystem.AnimateButtonPress(button, callback)
	local originalSize = button.Size
	
	local pressTween = TweenService:Create(button, TweenInfo.new(0.1), {
		Size = UDim2.new(originalSize.X.Scale * 0.95, originalSize.X.Offset, originalSize.Y.Scale * 0.95, originalSize.Y.Offset)
	})
	
	pressTween.Completed:Connect(function()
		local releaseTween = TweenService:Create(button, AnimationSystem.PRESETS.BOUNCE, {
			Size = originalSize
		})
		
		releaseTween.Completed:Connect(function()
			if callback then callback() end
		end)
		
		releaseTween:Play()
	end)
	
	pressTween:Play()
end

-- Create glow effect
function AnimationSystem.CreateGlowEffect(element, color, intensity)
	color = color or Color3.new(1, 1, 0)
	intensity = intensity or 0.5
	
	local glow = Instance.new("Frame")
	glow.Name = "GlowEffect"
	glow.Size = UDim2.new(1, 10, 1, 10)
	glow.Position = UDim2.new(0, -5, 0, -5)
	glow.BackgroundColor3 = color
	glow.BackgroundTransparency = 0.8
	glow.ZIndex = element.ZIndex - 1
	glow.Parent = element
	
	-- Add corner radius if parent has one
	local parentCorner = element:FindFirstChild("UICorner")
	if parentCorner then
		local glowCorner = Instance.new("UICorner")
		glowCorner.CornerRadius = UDim.new(parentCorner.CornerRadius.Scale, parentCorner.CornerRadius.Offset + 5)
		glowCorner.Parent = glow
	end
	
	-- Animate glow
	local glowTween = TweenService:Create(glow, AnimationSystem.PRESETS.GLOW_PULSE, {
		BackgroundTransparency = 0.8 - intensity
	})
	
	glowTween:Play()
	
	return glow, glowTween
end

-- Stop glow effect
function AnimationSystem.StopGlowEffect(element)
	local glow = element:FindFirstChild("GlowEffect")
	if glow then
		glow:Destroy()
	end
end

-- Animate notification appearance
function AnimationSystem.AnimateNotification(notification, callback)
	-- Start off-screen
	local targetPos = notification.Position
	notification.Position = UDim2.new(1, 0, targetPos.Y.Scale, targetPos.Y.Offset)
	notification.Visible = true
	
	-- Slide in with bounce
	local slideInTween = TweenService:Create(notification, AnimationSystem.PRESETS.SLIDE_IN, {
		Position = targetPos
	})
	
	slideInTween.Completed:Connect(function()
		if callback then callback() end
	end)
	
	slideInTween:Play()
	return slideInTween
end

-- Animate progress bar
function AnimationSystem.AnimateProgressBar(progressBar, targetProgress, duration)
	duration = duration or 0.5
	
	local tween = TweenService:Create(progressBar, TweenInfo.new(duration, Enum.EasingStyle.Quad), {
		Size = UDim2.new(targetProgress, 0, 1, 0)
	})
	
	tween:Play()
	return tween
end

-- Clean up all animations
function AnimationSystem.CleanupAnimations()
	for _, tween in pairs(activeAnimations) do
		if tween then
			tween:Cancel()
		end
	end
	activeAnimations = {}
end

return AnimationSystem
