--[[
	RemoteFunctions for UrbanSim
	All client-server communication functions that return values
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local RemoteFunctions = {}

-- Ensure Assets folder exists
local Assets = ReplicatedStorage:FindFirstChild("Assets")
if not Assets then
	Assets = Instance.new("Folder")
	Assets.Name = "Assets"
	Assets.Parent = ReplicatedStorage
end

-- Create RemoteFunctions (singleton pattern to prevent duplicates)
local function createRemoteFunction(name)
	-- Check if RemoteFunction already exists
	local existingFunction = Assets:FindFirstChild(name)
	if existingFunction and existingFunction:IsA("RemoteFunction") then
		print("📞 Using existing RemoteFunction:", name)
		return existingFunction
	end

	-- Create new RemoteFunction if it doesn't exist
	local func = Instance.new("RemoteFunction")
	func.Name = name
	func.Parent = Assets
	print("📞 Created new RemoteFunction:", name)
	return func
end

-- Data Functions
RemoteFunctions.GetPlayerData = createRemoteFunction("GetPlayerData")
RemoteFunctions.GetBuildingData = createRemoteFunction("GetBuildingData")
RemoteFunctions.GetResourceData = createRemoteFunction("GetResourceData")
RemoteFunctions.GetCraftingQueue = createRemoteFunction("GetCraftingQueue")
RemoteFunctions.GetAvailableRecipes = createRemoteFunction("GetAvailableRecipes")
RemoteFunctions.GetMaxCraftableQuantity = createRemoteFunction("GetMaxCraftableQuantity")

-- Building Functions
RemoteFunctions.CanPlaceBuilding = createRemoteFunction("CanPlaceBuilding")
RemoteFunctions.CanBuild = createRemoteFunction("CanBuild")
RemoteFunctions.GetBuildingCost = createRemoteFunction("GetBuildingCost")
RemoteFunctions.GetBuildingInfo = createRemoteFunction("GetBuildingInfo")
RemoteFunctions.DeleteBuildings = createRemoteFunction("DeleteBuildings")

-- Plot System Functions
RemoteFunctions.GetPlayerPlotInfo = createRemoteFunction("GetPlayerPlotInfo")
RemoteFunctions.GetPlotInfo = createRemoteFunction("GetPlotInfo")
RemoteFunctions.GetAllPlotsInfo = createRemoteFunction("GetAllPlotsInfo")
RemoteFunctions.ClaimPlot = createRemoteFunction("ClaimPlot")
RemoteFunctions.ReleasePlot = createRemoteFunction("ReleasePlot")
RemoteFunctions.TeleportToPlot = createRemoteFunction("TeleportToPlot")
RemoteFunctions.RenamePlot = createRemoteFunction("RenamePlot")
RemoteFunctions.CustomizePlotBorder = createRemoteFunction("CustomizePlotBorder")
RemoteFunctions.GetUpgradeCost = createRemoteFunction("GetUpgradeCost")

-- Economy Functions
RemoteFunctions.GetTaxAmount = createRemoteFunction("GetTaxAmount")
RemoteFunctions.CanAfford = createRemoteFunction("CanAfford")

-- Marketplace Functions
RemoteFunctions.GetMarketplaceListings = createRemoteFunction("GetMarketplaceListings")
RemoteFunctions.GetPlayerListings = createRemoteFunction("GetPlayerListings")

-- Clan Functions
RemoteFunctions.GetClanData = createRemoteFunction("GetClanData")
RemoteFunctions.GetClanList = createRemoteFunction("GetClanList")

-- Gamepass Functions
RemoteFunctions.HasGamepass = createRemoteFunction("HasGamepass")

-- Daily Rewards Functions
RemoteFunctions.GetDailyStatus = createRemoteFunction("GetDailyStatus")
RemoteFunctions.GetMinuteStatus = createRemoteFunction("GetMinuteStatus")

-- Achievement Functions
RemoteFunctions.GetPlayerAchievements = createRemoteFunction("GetPlayerAchievements")

-- Gamepass Functions
RemoteFunctions.GetPlayerGamepasses = createRemoteFunction("GetPlayerGamepasses")
RemoteFunctions.GetGamepassShop = createRemoteFunction("GetGamepassShop")

return RemoteFunctions
