--[[
	Mobile Building Controls
	Touch-optimized building placement and controls for mobile devices
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local camera = Workspace.CurrentCamera

-- Get shared modules
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))
local RemoteFunctions = require(Assets:WaitForChild("RemoteFunctions"))

local MobileBuildingControls = {}

-- Mobile state
local isMobile = UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
local isTablet = UserInputService.TouchEnabled and UserInputService.KeyboardEnabled
local isTouchDevice = UserInputService.TouchEnabled

-- Building state
local buildingMode = false
local removalMode = false
local selectedBuildingType = nil
local buildingRotation = 0
local buildingPreview = nil

-- Mobile UI elements
local mobileControlsGui = nil
local rotateLeftButton = nil
local rotateRightButton = nil
local confirmButton = nil
local cancelButton = nil
local buildingInfoPanel = nil

-- Touch tracking
local lastTouchPosition = nil
local touchStartTime = 0
local isDragging = false

print("📱 Mobile Building Controls initialized - Touch device:", isTouchDevice)

-- Create mobile building controls UI
function MobileBuildingControls.CreateMobileUI()
	if not isTouchDevice then return end
	
	-- Create mobile controls GUI
	mobileControlsGui = Instance.new("ScreenGui")
	mobileControlsGui.Name = "MobileBuildingControls"
	mobileControlsGui.ResetOnSpawn = false
	mobileControlsGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
	mobileControlsGui.Parent = playerGui
	
	-- Create control panel with enhanced visibility
	local controlPanel = Instance.new("Frame")
	controlPanel.Name = "ControlPanel"
	controlPanel.Size = UDim2.new(0, 300, 0, 80)
	controlPanel.Position = UDim2.new(0.5, -150, 1, -100)
	controlPanel.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	controlPanel.BackgroundTransparency = 0 -- Ensure it's visible
	controlPanel.BorderSizePixel = 0
	controlPanel.Visible = false
	controlPanel.ZIndex = 10 -- Ensure it's above other UI elements
	controlPanel.Parent = mobileControlsGui
	
	local controlCorner = Instance.new("UICorner")
	controlCorner.CornerRadius = UDim.new(0, 12)
	controlCorner.Parent = controlPanel
	
	-- Create button layout
	local buttonLayout = Instance.new("UIListLayout")
	buttonLayout.FillDirection = Enum.FillDirection.Horizontal
	buttonLayout.SortOrder = Enum.SortOrder.LayoutOrder
	buttonLayout.Padding = UDim.new(0, 10)
	buttonLayout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	buttonLayout.VerticalAlignment = Enum.VerticalAlignment.Center
	buttonLayout.Parent = controlPanel
	
	-- Rotate Left Button
	rotateLeftButton = MobileBuildingControls.CreateMobileButton("↺", "Rotate Left", 1)
	rotateLeftButton.Parent = controlPanel
	rotateLeftButton.MouseButton1Click:Connect(function()
		MobileBuildingControls.RotateBuilding(-90)
	end)
	
	-- Confirm Button
	confirmButton = MobileBuildingControls.CreateMobileButton("✓", "Place", 2)
	confirmButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
	confirmButton.Parent = controlPanel
	confirmButton.MouseButton1Click:Connect(function()
		MobileBuildingControls.ConfirmPlacement()
	end)
	
	-- Cancel Button
	cancelButton = MobileBuildingControls.CreateMobileButton("✕", "Cancel", 3)
	cancelButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	cancelButton.Parent = controlPanel
	cancelButton.MouseButton1Click:Connect(function()
		MobileBuildingControls.CancelBuilding()
	end)
	
	-- Rotate Right Button
	rotateRightButton = MobileBuildingControls.CreateMobileButton("↻", "Rotate Right", 4)
	rotateRightButton.Parent = controlPanel
	rotateRightButton.MouseButton1Click:Connect(function()
		MobileBuildingControls.RotateBuilding(90)
	end)
	
	-- Create building info panel with enhanced visibility
	buildingInfoPanel = Instance.new("Frame")
	buildingInfoPanel.Name = "BuildingInfoPanel"
	buildingInfoPanel.Size = UDim2.new(0, 250, 0, 60)
	buildingInfoPanel.Position = UDim2.new(0.5, -125, 0, 20)
	buildingInfoPanel.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	buildingInfoPanel.BackgroundTransparency = 0 -- Ensure it's visible
	buildingInfoPanel.BorderSizePixel = 0
	buildingInfoPanel.Visible = false
	buildingInfoPanel.ZIndex = 10 -- Ensure it's above other UI elements
	buildingInfoPanel.Parent = mobileControlsGui
	
	local infoCorner = Instance.new("UICorner")
	infoCorner.CornerRadius = UDim.new(0, 8)
	infoCorner.Parent = buildingInfoPanel
	
	local infoText = Instance.new("TextLabel")
	infoText.Name = "InfoText"
	infoText.Size = UDim2.new(1, -20, 1, -10)
	infoText.Position = UDim2.new(0, 10, 0, 5)
	infoText.BackgroundTransparency = 1
	infoText.Text = "Tap to place building"
	infoText.TextColor3 = Color3.new(1, 1, 1)
	infoText.TextSize = 16
	infoText.Font = Enum.Font.SourceSans
	infoText.TextWrapped = true
	infoText.Parent = buildingInfoPanel
	
	print("📱 Mobile building controls UI created")
end

-- Create mobile button
function MobileBuildingControls.CreateMobileButton(text, tooltip, layoutOrder)
	local button = Instance.new("TextButton")
	button.Size = UDim2.new(0, 60, 0, 60)
	button.BackgroundColor3 = Color3.new(0.2, 0.2, 0.3)
	button.Text = text
	button.TextColor3 = Color3.new(1, 1, 1)
	button.TextSize = 24
	button.Font = Enum.Font.SourceSansBold
	button.LayoutOrder = layoutOrder
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = button
	
	-- Touch feedback
	button.MouseButton1Down:Connect(function()
		TweenService:Create(button, TweenInfo.new(0.1), {
			Size = UDim2.new(0, 55, 0, 55),
			BackgroundColor3 = button.BackgroundColor3:lerp(Color3.new(1, 1, 1), 0.2)
		}):Play()
	end)
	
	button.MouseButton1Up:Connect(function()
		TweenService:Create(button, TweenInfo.new(0.1), {
			Size = UDim2.new(0, 60, 0, 60),
			BackgroundColor3 = button.BackgroundColor3:lerp(Color3.new(0, 0, 0), 0.2)
		}):Play()
	end)
	
	return button
end

-- Show mobile controls
function MobileBuildingControls.ShowControls()
	if not isTouchDevice or not mobileControlsGui then return end
	
	local controlPanel = mobileControlsGui:FindFirstChild("ControlPanel")
	if controlPanel then
		controlPanel.Visible = true
		controlPanel.Position = UDim2.new(0.5, -150, 1, -100)
		
		TweenService:Create(controlPanel, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
			Position = UDim2.new(0.5, -150, 1, -100)
		}):Play()
	end
	
	if buildingInfoPanel then
		buildingInfoPanel.Visible = true
		local infoText = buildingInfoPanel:FindFirstChild("InfoText")
		if infoText and selectedBuildingType then
			infoText.Text = "Placing: " .. selectedBuildingType .. "\nTap to place, drag to move"
		end
	end
end

-- Hide mobile controls
function MobileBuildingControls.HideControls()
	if not isTouchDevice or not mobileControlsGui then return end
	
	local controlPanel = mobileControlsGui:FindFirstChild("ControlPanel")
	if controlPanel then
		TweenService:Create(controlPanel, TweenInfo.new(0.2), {
			Position = UDim2.new(0.5, -150, 1, 20)
		}):Play()
		
		task.wait(0.2)
		controlPanel.Visible = false
	end
	
	if buildingInfoPanel then
		buildingInfoPanel.Visible = false
	end
end

-- Start building mode
function MobileBuildingControls.StartBuildingMode(buildingType)
	if not isTouchDevice then return end
	
	buildingMode = true
	removalMode = false
	selectedBuildingType = buildingType
	buildingRotation = 0
	
	MobileBuildingControls.ShowControls()
	print("📱 Started mobile building mode:", buildingType)
end

-- Start removal mode
function MobileBuildingControls.StartRemovalMode(buildingType)
	if not isTouchDevice then return end
	
	buildingMode = false
	removalMode = true
	selectedBuildingType = buildingType
	
	if buildingInfoPanel then
		buildingInfoPanel.Visible = true
		local infoText = buildingInfoPanel:FindFirstChild("InfoText")
		if infoText then
			infoText.Text = "Removal Mode: " .. buildingType .. "\nTap building to remove"
		end
	end
	
	print("📱 Started mobile removal mode:", buildingType)
end

-- Rotate building
function MobileBuildingControls.RotateBuilding(degrees)
	buildingRotation = (buildingRotation + degrees) % 360
	print("📱 Rotated building to:", buildingRotation, "degrees")
	
	-- Update preview if it exists
	if buildingPreview then
		local primaryPart = buildingPreview.PrimaryPart or buildingPreview:FindFirstChildOfClass("Part")
		if primaryPart then
			local currentCFrame = primaryPart.CFrame
			local newCFrame = CFrame.new(currentCFrame.Position) * CFrame.Angles(0, math.rad(buildingRotation), 0)
			
			if buildingPreview.PrimaryPart then
				buildingPreview:SetPrimaryPartCFrame(newCFrame)
			else
				primaryPart.CFrame = newCFrame
			end
		end
	end
end

-- Confirm placement
function MobileBuildingControls.ConfirmPlacement()
	if not buildingMode or not selectedBuildingType or not lastTouchPosition then return end
	
	print("📱 Confirming building placement at:", lastTouchPosition)
	
	-- Fire placement event
	if RemoteEvents.PlaceBuilding then
		RemoteEvents.PlaceBuilding:FireServer(selectedBuildingType, lastTouchPosition, buildingRotation)
	end
	
	MobileBuildingControls.CancelBuilding()
end

-- Cancel building
function MobileBuildingControls.CancelBuilding()
	buildingMode = false
	removalMode = false
	selectedBuildingType = nil
	buildingRotation = 0
	lastTouchPosition = nil
	
	-- Clean up preview
	if buildingPreview then
		buildingPreview:Destroy()
		buildingPreview = nil
	end
	
	MobileBuildingControls.HideControls()
	print("📱 Cancelled mobile building mode")
end

-- Handle touch input for building placement
function MobileBuildingControls.HandleTouchInput()
	if not isTouchDevice then return end

	UserInputService.TouchTap:Connect(function(touchPositions, gameProcessed)
		if gameProcessed then return end

		if buildingMode and selectedBuildingType then
			-- Get touch position
			local touchPosition = touchPositions[1]
			if touchPosition then
				-- Convert touch position to world position
				local ray = camera:ScreenPointToRay(touchPosition.X, touchPosition.Y)
				local raycastParams = RaycastParams.new()
				raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
				raycastParams.FilterDescendantsInstances = {camera, player.Character}

				local raycastResult = Workspace:Raycast(ray.Origin, ray.Direction * 1000, raycastParams)
				if raycastResult then
					lastTouchPosition = raycastResult.Position
					print("📱 Touch detected at:", lastTouchPosition)

					-- Auto-confirm placement on touch
					MobileBuildingControls.ConfirmPlacement()
				end
			end
		elseif removalMode and selectedBuildingType then
			-- Handle building removal on touch
			local touchPosition = touchPositions[1]
			if touchPosition then
				local ray = camera:ScreenPointToRay(touchPosition.X, touchPosition.Y)
				local raycastParams = RaycastParams.new()
				raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
				raycastParams.FilterDescendantsInstances = {camera}

				local raycastResult = Workspace:Raycast(ray.Origin, ray.Direction * 1000, raycastParams)
				if raycastResult and raycastResult.Instance then
					-- Check if touched object is a building
					local model = raycastResult.Instance.Parent
					if model and model:FindFirstChild("BuildingInfo") then
						local buildingId = model.BuildingInfo.Value
						print("📱 Touched building for removal:", buildingId)

						-- Fire removal event
						if RemoteEvents.RemoveBuilding then
							RemoteEvents.RemoveBuilding:FireServer(buildingId)
						end

						MobileBuildingControls.CancelBuilding()
					end
				end
			end
		end
	end)
end

-- Initialize mobile controls
function MobileBuildingControls.Initialize()
	if not isTouchDevice then
		print("📱 Not a touch device, skipping mobile controls")
		return
	end

	MobileBuildingControls.CreateMobileUI()
	MobileBuildingControls.HandleTouchInput()
	print("📱 Mobile Building Controls initialized successfully")
end

return MobileBuildingControls
