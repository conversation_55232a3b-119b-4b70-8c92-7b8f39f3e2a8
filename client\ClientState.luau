-- ClientState.luau
-- Client-side state management for UrbanSim

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

local ClientState = {}

-- Building system state
ClientState.buildingMode = false
ClientState.selectedBuildingType = nil
ClientState.buildingRotation = 0
ClientState.buildingPreview = nil

-- UI state
ClientState.craftingWindowOpen = false
ClientState.buildingWindowOpen = false
ClientState.plotWindowOpen = false
ClientState.statisticsWindowOpen = false

-- Deletion system state
ClientState.deletionMode = false
ClientState.selectedBuildings = {}

-- Mobile detection
ClientState.isMobile = UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
ClientState.isTablet = UserInputService.TouchEnabled and UserInputService.KeyboardEnabled
ClientState.isTouchDevice = UserInputService.TouchEnabled

-- Player data cache (from leaderstats)
ClientState.playerData = {
	Pieces = 0,
	Cash = 0,
	XP = 0,
	Population = 0,
	Level = 1,
	Energy = 0,
	Water = 0
}

-- Initialize ClientState
function ClientState.Initialize()
	print("🎮 Initializing ClientState...")
	
	-- Set up leaderstats monitoring
	ClientState.SetupLeaderstatsMonitoring()
	
	-- Set up input handling
	ClientState.SetupInputHandling()
	
	-- Make ClientState globally accessible
	_G.ClientState = ClientState
	
	print("✅ ClientState initialized successfully")
	print("📱 Device detection - Mobile:", ClientState.isMobile, "Tablet:", ClientState.isTablet, "Touch:", ClientState.isTouchDevice)
end

-- Set up leaderstats monitoring
function ClientState.SetupLeaderstatsMonitoring()
	local leaderstats = player:WaitForChild("leaderstats", 10)
	if leaderstats then
		print("📊 Setting up leaderstats monitoring...")
		
		-- Initial data load
		ClientState.UpdatePlayerDataFromLeaderstats()
		
		-- Monitor changes
		for _, stat in pairs(leaderstats:GetChildren()) do
			if stat:IsA("IntValue") or stat:IsA("NumberValue") then
				stat.Changed:Connect(function()
					ClientState.UpdatePlayerDataFromLeaderstats()
				end)
			end
		end
		
		print("✅ Leaderstats monitoring set up")
	else
		warn("⚠️ Leaderstats not found after 10 seconds")
	end
end

-- Update player data from leaderstats
function ClientState.UpdatePlayerDataFromLeaderstats()
	local leaderstats = player:FindFirstChild("leaderstats")
	if leaderstats then
		for _, stat in pairs(leaderstats:GetChildren()) do
			if stat:IsA("IntValue") or stat:IsA("NumberValue") then
				ClientState.playerData[stat.Name] = stat.Value
			end
		end
	end
end

-- Set up input handling for building system
function ClientState.SetupInputHandling()
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		-- Handle building rotation
		if ClientState.buildingMode then
			if input.KeyCode == Enum.KeyCode.R then
				ClientState.RotateBuilding(90) -- Clockwise
			elseif input.KeyCode == Enum.KeyCode.E then
				ClientState.RotateBuilding(-90) -- Counter-clockwise
			elseif input.KeyCode == Enum.KeyCode.Q then
				ClientState.CancelBuildingMode()
			end
		end
		
		-- Handle deletion mode
		if input.KeyCode == Enum.KeyCode.Delete then
			ClientState.ToggleDeletionMode()
		end
	end)
end

-- Building system functions
function ClientState.StartBuildingMode(buildingType)
	print("🏗️ Starting building mode:", buildingType)
	
	ClientState.buildingMode = true
	ClientState.selectedBuildingType = buildingType
	ClientState.deletionMode = false
	
	-- Clear any existing preview
	ClientState.ClearBuildingPreview()
	
	print("✅ Building mode started for:", buildingType)
end

function ClientState.CancelBuildingMode()
	print("🏗️ Canceling building mode")
	
	ClientState.buildingMode = false
	ClientState.selectedBuildingType = nil
	
	-- Clear preview
	ClientState.ClearBuildingPreview()
	
	-- Hide status indicator
	local playerGui = player:WaitForChild("PlayerGui")
	local statusIndicator = playerGui:FindFirstChild("UrbanSimUI") and playerGui.UrbanSimUI:FindFirstChild("StatusIndicator")
	if statusIndicator then
		statusIndicator.Visible = false
	end
	
	print("✅ Building mode canceled")
end

function ClientState.RotateBuilding(degrees)
	ClientState.buildingRotation = (ClientState.buildingRotation + degrees) % 360
	print("🔄 Building rotation:", ClientState.buildingRotation)
	
	-- Update preview if it exists
	if ClientState.buildingPreview then
		-- The preview update will be handled by the main building system
		print("🔄 Rotation updated, preview will be refreshed")
	end
end

function ClientState.ClearBuildingPreview()
	if ClientState.buildingPreview then
		ClientState.buildingPreview:Destroy()
		ClientState.buildingPreview = nil
	end
end

-- Deletion system functions
function ClientState.ToggleDeletionMode()
	ClientState.deletionMode = not ClientState.deletionMode
	
	if ClientState.deletionMode then
		-- Cancel building mode when entering deletion mode
		ClientState.CancelBuildingMode()
		print("🗑️ Deletion mode activated")
	else
		-- Clear selected buildings
		ClientState.ClearSelectedBuildings()
		print("🗑️ Deletion mode deactivated")
	end
end

function ClientState.AddSelectedBuilding(buildingId, buildingModel)
	ClientState.selectedBuildings[buildingId] = buildingModel
	print("🗑️ Added building to selection:", buildingId)
end

function ClientState.RemoveSelectedBuilding(buildingId)
	ClientState.selectedBuildings[buildingId] = nil
	print("🗑️ Removed building from selection:", buildingId)
end

function ClientState.ClearSelectedBuildings()
	ClientState.selectedBuildings = {}
	print("🗑️ Cleared all selected buildings")
end

function ClientState.GetSelectedBuildingIds()
	local ids = {}
	for buildingId, _ in pairs(ClientState.selectedBuildings) do
		table.insert(ids, buildingId)
	end
	return ids
end

-- UI state management
function ClientState.SetWindowState(windowName, isOpen)
	local stateKey = windowName .. "WindowOpen"
	if ClientState[stateKey] ~= nil then
		ClientState[stateKey] = isOpen
		print("🪟 Window state updated:", windowName, "=", isOpen)
	end
end

function ClientState.IsWindowOpen(windowName)
	local stateKey = windowName .. "WindowOpen"
	return ClientState[stateKey] or false
end

-- Player data functions
function ClientState.GetPlayerData()
	return ClientState.playerData
end

function ClientState.GetPlayerStat(statName)
	return ClientState.playerData[statName] or 0
end

function ClientState.CanAfford(cost)
	if not cost then return true end
	
	for currency, amount in pairs(cost) do
		local playerAmount = ClientState.GetPlayerStat(currency)
		if playerAmount < amount then
			return false, currency, amount, playerAmount
		end
	end
	
	return true
end

-- Device detection functions
function ClientState.IsMobile()
	return ClientState.isMobile
end

function ClientState.IsTablet()
	return ClientState.isTablet
end

function ClientState.IsTouchDevice()
	return ClientState.isTouchDevice
end

-- Debug functions
function ClientState.DebugState()
	print("🔍 ClientState Debug Info:")
	print("=" .. string.rep("=", 40))
	
	print("\n🏗️ Building State:")
	print("  - buildingMode:", ClientState.buildingMode)
	print("  - selectedBuildingType:", ClientState.selectedBuildingType or "nil")
	print("  - buildingRotation:", ClientState.buildingRotation)
	print("  - buildingPreview exists:", ClientState.buildingPreview and "YES" or "NO")
	
	print("\n🗑️ Deletion State:")
	print("  - deletionMode:", ClientState.deletionMode)
	local selectedCount = 0
	for _ in pairs(ClientState.selectedBuildings) do
		selectedCount = selectedCount + 1
	end
	print("  - selectedBuildings count:", selectedCount)
	
	print("\n🪟 UI State:")
	print("  - craftingWindowOpen:", ClientState.craftingWindowOpen)
	print("  - buildingWindowOpen:", ClientState.buildingWindowOpen)
	print("  - plotWindowOpen:", ClientState.plotWindowOpen)
	print("  - statisticsWindowOpen:", ClientState.statisticsWindowOpen)
	
	print("\n📱 Device Info:")
	print("  - isMobile:", ClientState.isMobile)
	print("  - isTablet:", ClientState.isTablet)
	print("  - isTouchDevice:", ClientState.isTouchDevice)
	
	print("\n💰 Player Data:")
	for stat, value in pairs(ClientState.playerData) do
		print("  - " .. stat .. ":", value)
	end
end

-- Reset all state (useful for debugging)
function ClientState.Reset()
	print("🔄 Resetting ClientState...")
	
	-- Reset building state
	ClientState.buildingMode = false
	ClientState.selectedBuildingType = nil
	ClientState.buildingRotation = 0
	ClientState.ClearBuildingPreview()
	
	-- Reset deletion state
	ClientState.deletionMode = false
	ClientState.ClearSelectedBuildings()
	
	-- Reset UI state
	ClientState.craftingWindowOpen = false
	ClientState.buildingWindowOpen = false
	ClientState.plotWindowOpen = false
	ClientState.statisticsWindowOpen = false
	
	print("✅ ClientState reset complete")
end

-- Make debug functions globally accessible
_G.DebugClientState = ClientState.DebugState
_G.ResetClientState = ClientState.Reset

return ClientState
