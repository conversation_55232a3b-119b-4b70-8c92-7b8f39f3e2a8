-- SoundEvents.luau
-- Server-side sound event management

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for shared modules
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitFor<PERSON>hild("RemoteEvents"))

local SoundEvents = {}

-- Sound event types
local SOUND_EVENTS = {
	BUILDING_PLACED = "BuildingPlaced",
	BUILDING_UPGRADED = "BuildingUpgraded", 
	BUILDING_DEMOLISHED = "BuildingDemolished",
	MONEY_EARNED = "MoneyEarned",
	LEVEL_UP = "LevelUp",
	ACHIEVEMENT_UNLOCKED = "AchievementUnlocked",
	RESOURCE_COLLECTED = "ResourceCollected",
	PLOT_CLAIMED = "PlotClaimed",
	PLOT_RELEASED = "PlotReleased",
	NOTIFICATION = "Notification",
	AMBIENT_CHANGE = "AmbientChange"
}

-- Initialize sound events system
function SoundEvents.Initialize()
	print("🔊 Initializing Sound Events...")

	-- Create RemoteEvents for sound if they don't exist
	SoundEvents.CreateSoundRemoteEvents()

	-- Verify all sound events were created
	SoundEvents.VerifySoundEvents()

	print("✅ Sound Events initialized!")
end

-- Verify all sound events exist
function SoundEvents.VerifySoundEvents()
	local soundEventsFolder = Assets:FindFirstChild("SoundEvents")
	if not soundEventsFolder then
		warn("🔊 SoundEvents folder not found after creation!")
		return
	end

	print("🔊 Verifying sound events:")
	for eventName, eventValue in pairs(SOUND_EVENTS) do
		local remoteEvent = soundEventsFolder:FindFirstChild(eventValue)
		if remoteEvent then
			print("  ✅", eventValue, "- Found")
		else
			print("  ❌", eventValue, "- Missing, creating now...")
			local newRemoteEvent = Instance.new("RemoteEvent")
			newRemoteEvent.Name = eventValue
			newRemoteEvent.Parent = soundEventsFolder
			print("  ✅", eventValue, "- Created")
		end
	end
end

-- Create sound-specific RemoteEvents
function SoundEvents.CreateSoundRemoteEvents()
	-- Check if sound events already exist, if not create them
	local soundEventsFolder = Assets:FindFirstChild("SoundEvents")
	if not soundEventsFolder then
		soundEventsFolder = Instance.new("Folder")
		soundEventsFolder.Name = "SoundEvents"
		soundEventsFolder.Parent = Assets
		print("🔊 Created SoundEvents folder")
	end

	-- Create individual sound events using the correct event values
	for eventName, eventValue in pairs(SOUND_EVENTS) do
		if not soundEventsFolder:FindFirstChild(eventValue) then
			local remoteEvent = Instance.new("RemoteEvent")
			remoteEvent.Name = eventValue -- Use the value, not the key
			remoteEvent.Parent = soundEventsFolder
			print("🔊 Created sound event:", eventValue)
		end
	end
end

-- Fire sound event to specific player
function SoundEvents.FireToPlayer(player, eventType, data)
	if not player or not player.Parent then return end

	local soundEventsFolder = Assets:FindFirstChild("SoundEvents")
	if not soundEventsFolder then
		warn("🔊 SoundEvents folder not found in Assets")
		return
	end

	local remoteEvent = soundEventsFolder:FindFirstChild(eventType)
	if remoteEvent then
		remoteEvent:FireClient(player, data)
		print("🔊 Fired sound event", eventType, "to", player.Name)
	else
		warn("🔊 Sound event not found:", eventType)
		print("🔊 Available sound events:")
		for _, child in pairs(soundEventsFolder:GetChildren()) do
			print("  -", child.Name)
		end

		-- Try to create the missing sound event
		print("🔊 Creating missing sound event:", eventType)
		local newRemoteEvent = Instance.new("RemoteEvent")
		newRemoteEvent.Name = eventType
		newRemoteEvent.Parent = soundEventsFolder

		-- Fire the event now that it exists
		newRemoteEvent:FireClient(player, data)
		print("🔊 Created and fired sound event", eventType, "to", player.Name)
	end
end

-- Fire sound event to all players
function SoundEvents.FireToAllPlayers(eventType, data)
	local soundEventsFolder = Assets:FindFirstChild("SoundEvents")
	if not soundEventsFolder then
		warn("🔊 SoundEvents folder not found in Assets")
		return
	end

	local remoteEvent = soundEventsFolder:FindFirstChild(eventType)
	if remoteEvent then
		remoteEvent:FireAllClients(data)
		print("🔊 Fired sound event", eventType, "to all players")
	else
		warn("🔊 Sound event not found:", eventType)

		-- Try to create the missing sound event
		print("🔊 Creating missing sound event:", eventType)
		local newRemoteEvent = Instance.new("RemoteEvent")
		newRemoteEvent.Name = eventType
		newRemoteEvent.Parent = soundEventsFolder

		-- Fire the event now that it exists
		newRemoteEvent:FireAllClients(data)
		print("🔊 Created and fired sound event", eventType, "to all players")
	end
end

-- Fire sound event to players in range
function SoundEvents.FireToPlayersInRange(position, range, eventType, data)
	for _, player in ipairs(Players:GetPlayers()) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			local distance = (player.Character.HumanoidRootPart.Position - position).Magnitude
			if distance <= range then
				SoundEvents.FireToPlayer(player, eventType, data)
			end
		end
	end
	
	print("🔊 Fired sound event", eventType, "to players in range of", position)
end

-- Building placed sound
function SoundEvents.OnBuildingPlaced(player, buildingData)
	-- Fire to the player who placed the building
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.BUILDING_PLACED, {
		buildingType = buildingData.Type,
		position = buildingData.Position
	})
	
	-- Fire to nearby players with positional audio
	if buildingData.Position then
		local position = Vector3.new(buildingData.Position.X, buildingData.Position.Y, buildingData.Position.Z)
		SoundEvents.FireToPlayersInRange(position, 100, SOUND_EVENTS.BUILDING_PLACED, {
			buildingType = buildingData.Type,
			position = buildingData.Position,
			positional = true
		})
	end
end

-- Building upgraded sound
function SoundEvents.OnBuildingUpgraded(player, buildingData)
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.BUILDING_UPGRADED, {
		buildingType = buildingData.Type,
		level = buildingData.Level,
		position = buildingData.Position
	})
end

-- Building demolished sound
function SoundEvents.OnBuildingDemolished(player, buildingData)
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.BUILDING_DEMOLISHED, {
		buildingType = buildingData.Type,
		position = buildingData.Position
	})
	
	-- Fire to nearby players
	if buildingData.Position then
		local position = Vector3.new(buildingData.Position.X, buildingData.Position.Y, buildingData.Position.Z)
		SoundEvents.FireToPlayersInRange(position, 100, SOUND_EVENTS.BUILDING_DEMOLISHED, {
			buildingType = buildingData.Type,
			position = buildingData.Position,
			positional = true
		})
	end
end

-- Money earned sound
function SoundEvents.OnMoneyEarned(player, amount, source)
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.MONEY_EARNED, {
		amount = amount,
		source = source
	})
end

-- Level up sound
function SoundEvents.OnLevelUp(player, newLevel)
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.LEVEL_UP, {
		level = newLevel
	})
end

-- Achievement unlocked sound
function SoundEvents.OnAchievementUnlocked(player, achievementName)
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.ACHIEVEMENT_UNLOCKED, {
		achievement = achievementName
	})
end

-- Resource collected sound
function SoundEvents.OnResourceCollected(player, resourceType, amount)
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.RESOURCE_COLLECTED, {
		resourceType = resourceType,
		amount = amount
	})
end

-- Plot claimed sound
function SoundEvents.OnPlotClaimed(player, plotNumber)
	-- Fire to the player who claimed
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.PLOT_CLAIMED, {
		plotNumber = plotNumber,
		playerName = player.Name,
		isOwner = true
	})
	
	-- Fire to all other players
	for _, otherPlayer in ipairs(Players:GetPlayers()) do
		if otherPlayer ~= player then
			SoundEvents.FireToPlayer(otherPlayer, SOUND_EVENTS.PLOT_CLAIMED, {
				plotNumber = plotNumber,
				playerName = player.Name,
				isOwner = false
			})
		end
	end
end

-- Plot released sound
function SoundEvents.OnPlotReleased(player, plotNumber)
	SoundEvents.FireToAllPlayers(SOUND_EVENTS.PLOT_RELEASED, {
		plotNumber = plotNumber,
		playerName = player.Name
	})
end

-- Notification sound
function SoundEvents.OnNotification(player, notificationType, message)
	SoundEvents.FireToPlayer(player, SOUND_EVENTS.NOTIFICATION, {
		type = notificationType,
		message = message
	})
end

-- Ambient change sound
function SoundEvents.OnAmbientChange(ambientType, position, range)
	if position and range then
		SoundEvents.FireToPlayersInRange(position, range, SOUND_EVENTS.AMBIENT_CHANGE, {
			ambientType = ambientType,
			position = position
		})
	else
		SoundEvents.FireToAllPlayers(SOUND_EVENTS.AMBIENT_CHANGE, {
			ambientType = ambientType
		})
	end
end

-- Integration helpers for existing systems
function SoundEvents.IntegrateWithBuildingManager()
	-- This would be called from BuildingManager when buildings are placed/upgraded/demolished
	-- Example integration points:
	-- BuildingManager.OnBuildingPlaced:Connect(SoundEvents.OnBuildingPlaced)
	-- BuildingManager.OnBuildingUpgraded:Connect(SoundEvents.OnBuildingUpgraded)
	-- BuildingManager.OnBuildingDemolished:Connect(SoundEvents.OnBuildingDemolished)
end

function SoundEvents.IntegrateWithDataManager()
	-- This would be called from DataManager when player data changes
	-- Example integration points:
	-- DataManager.OnMoneyChanged:Connect(SoundEvents.OnMoneyEarned)
	-- DataManager.OnLevelUp:Connect(SoundEvents.OnLevelUp)
end

function SoundEvents.IntegrateWithPlotManager()
	-- This would be called from PlotManager when plots are claimed/released
	-- Example integration points:
	-- PlotManager.OnPlotClaimed:Connect(SoundEvents.OnPlotClaimed)
	-- PlotManager.OnPlotReleased:Connect(SoundEvents.OnPlotReleased)
end

-- Get sound events for client integration
function SoundEvents.GetSoundEvents()
	return SOUND_EVENTS
end

return SoundEvents
