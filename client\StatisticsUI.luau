-- StatisticsUI.luau
-- Client-side statistics frame system

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON>hild("PlayerGui")

local StatisticsUI = {}

-- Statistics state
local statisticsFrame = nil
local statisticsButton = nil
local isOpen = false

-- Create statistics toggle button
function StatisticsUI.CreateStatisticsButton()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then
		warn("📊 UrbanSimUI not found for statistics button!")
		return
	end

	-- Create statistics button (replaces TopBar)
	statisticsButton = Instance.new("TextButton")
	statisticsButton.Name = "StatisticsButton"
	statisticsButton.Size = UDim2.new(0, 150, 0, 40)
	statisticsButton.Position = UDim2.new(0, 10, 0, 10)
	statisticsButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
	statisticsButton.Text = "📊 Statistics"
	statisticsButton.TextColor3 = Color3.new(1, 1, 1)
	statisticsButton.TextScaled = true
	statisticsButton.Font = Enum.Font.SourceSansBold
	statisticsButton.ZIndex = 5
	statisticsButton.Parent = screenGui

	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = statisticsButton

	-- Button functionality
	statisticsButton.MouseButton1Click:Connect(function()
		StatisticsUI.ToggleStatistics()
	end)

	-- Hover effects
	statisticsButton.MouseEnter:Connect(function()
		TweenService:Create(statisticsButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.3, 0.7, 0.9)
		}):Play()
	end)

	statisticsButton.MouseLeave:Connect(function()
		TweenService:Create(statisticsButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
		}):Play()
	end)

	print("📊 Statistics button created!")
end

-- Create statistics frame
function StatisticsUI.CreateStatisticsFrame()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end

	-- Create main statistics frame
	statisticsFrame = Instance.new("Frame")
	statisticsFrame.Name = "StatisticsFrame"
	statisticsFrame.Size = UDim2.new(1, 0, 0, 80)
	statisticsFrame.Position = UDim2.new(0, 0, 0, 0)
	statisticsFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	statisticsFrame.BorderSizePixel = 0
	statisticsFrame.Visible = false
	statisticsFrame.ZIndex = 3
	statisticsFrame.Parent = screenGui

	-- Add gradient
	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.15, 0.15, 0.2)),
		ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.1, 0.15))
	}
	gradient.Rotation = 90
	gradient.Parent = statisticsFrame

	-- Create currency container with proper layout and overflow handling
	local currencyContainer = Instance.new("ScrollingFrame")
	currencyContainer.Name = "CurrencyContainer"
	currencyContainer.Size = UDim2.new(1, -420, 1, -10) -- Leave space for buttons
	currencyContainer.Position = UDim2.new(0, 5, 0, 5)
	currencyContainer.BackgroundTransparency = 1
	currencyContainer.BorderSizePixel = 0
	currencyContainer.ScrollBarThickness = 0 -- Hide scrollbar for cleaner look
	currencyContainer.ScrollingDirection = Enum.ScrollingDirection.X -- Horizontal scrolling
	currencyContainer.CanvasSize = UDim2.new(0, 0, 1, 0) -- Will be updated automatically
	currencyContainer.Parent = statisticsFrame

	-- Add UIListLayout for automatic currency arrangement
	local currencyLayout = Instance.new("UIListLayout")
	currencyLayout.FillDirection = Enum.FillDirection.Horizontal
	currencyLayout.SortOrder = Enum.SortOrder.LayoutOrder
	currencyLayout.VerticalAlignment = Enum.VerticalAlignment.Center
	currencyLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
	currencyLayout.Parent = currencyContainer

	-- Mobile detection for responsive design
	local isMobile = UserInputService.TouchEnabled and not UserInputService.KeyboardEnabled
	local isTablet = UserInputService.TouchEnabled and UserInputService.KeyboardEnabled

	-- Responsive padding based on device type
	local paddingSize = isMobile and 3 or (isTablet and 5 or 8)
	currencyLayout.Padding = UDim.new(0, paddingSize)

	-- Add padding to container
	local containerPadding = Instance.new("UIPadding")
	containerPadding.PaddingAll = UDim.new(0, 5)
	containerPadding.Parent = currencyContainer

	-- Auto-update canvas size when content changes
	currencyLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
		currencyContainer.CanvasSize = UDim2.new(0, currencyLayout.AbsoluteContentSize.X + 10, 1, 0)
	end)

	-- Currency data
	local currencies = {"Pieces", "Cash", "XP", "Population", "Level", "Energy", "Water"}
	local currencyIcons = {"💰", "💎", "⭐", "👥", "🏆", "⚡", "💧"}

	-- Create currency frames with proper layout
	for i, currency in ipairs(currencies) do
		local currencyFrame = Instance.new("Frame")
		currencyFrame.Name = currency .. "Frame"
		currencyFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
		currencyFrame.BorderSizePixel = 0
		currencyFrame.LayoutOrder = i
		currencyFrame.Parent = currencyContainer

		-- Responsive sizing based on device type with constraints
		local frameWidth = isMobile and 85 or (isTablet and 95 or 100)
		currencyFrame.Size = UDim2.new(0, frameWidth, 1, -6)

		-- Add size constraints to prevent issues
		local sizeConstraint = Instance.new("UISizeConstraint")
		sizeConstraint.MinSize = Vector2.new(80, 30) -- Minimum size
		sizeConstraint.MaxSize = Vector2.new(120, 70) -- Maximum size
		sizeConstraint.Parent = currencyFrame

		-- Add corner radius
		local currencyCorner = Instance.new("UICorner")
		currencyCorner.CornerRadius = UDim.new(0, 8)
		currencyCorner.Parent = currencyFrame

		-- Add gradient
		local currencyGradient = Instance.new("UIGradient")
		currencyGradient.Color = ColorSequence.new{
			ColorSequenceKeypoint.new(0, Color3.new(0.2, 0.2, 0.25)),
			ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.1, 0.15))
		}
		currencyGradient.Rotation = 90
		currencyGradient.Parent = currencyFrame

		-- Currency icon
		local currencyIcon = Instance.new("TextLabel")
		currencyIcon.Name = currency .. "Icon"
		currencyIcon.Size = UDim2.new(0, 25, 1, 0)
		currencyIcon.Position = UDim2.new(0, 5, 0, 0)
		currencyIcon.BackgroundTransparency = 1
		currencyIcon.Text = currencyIcons[i]
		currencyIcon.TextColor3 = Color3.new(1, 1, 1)
		currencyIcon.TextScaled = true
		currencyIcon.Font = Enum.Font.SourceSansBold
		currencyIcon.Parent = currencyFrame

		-- Currency label
		local currencyLabel = Instance.new("TextLabel")
		currencyLabel.Name = currency .. "Label"
		currencyLabel.Size = UDim2.new(1, -30, 1, 0)
		currencyLabel.Position = UDim2.new(0, 30, 0, 0)
		currencyLabel.BackgroundTransparency = 1
		currencyLabel.Text = "0"
		currencyLabel.TextColor3 = Color3.new(1, 1, 1)
		currencyLabel.TextScaled = true
		currencyLabel.Font = Enum.Font.SourceSansBold
		currencyLabel.TextXAlignment = Enum.TextXAlignment.Left
		currencyLabel.Parent = currencyFrame

		-- Add hover effect
		currencyFrame.MouseEnter:Connect(function()
			TweenService:Create(currencyFrame, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
			}):Play()
		end)

		currencyFrame.MouseLeave:Connect(function()
			TweenService:Create(currencyFrame, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
			}):Play()
		end)
	end

	-- Action buttons section with proper layout and responsive design
	local buttonsFrame = Instance.new("Frame")
	buttonsFrame.Name = "ButtonsFrame"
	buttonsFrame.BackgroundTransparency = 1
	buttonsFrame.Parent = statisticsFrame

	-- Responsive button frame sizing
	local buttonFrameWidth = isMobile and 280 or (isTablet and 320 or 350)
	buttonsFrame.Size = UDim2.new(0, buttonFrameWidth, 1, -10)
	buttonsFrame.Position = UDim2.new(1, -(buttonFrameWidth + 10), 0, 5)

	-- Add UIListLayout for automatic button arrangement
	local buttonLayout = Instance.new("UIListLayout")
	buttonLayout.FillDirection = Enum.FillDirection.Horizontal
	buttonLayout.SortOrder = Enum.SortOrder.LayoutOrder
	buttonLayout.VerticalAlignment = Enum.VerticalAlignment.Center
	buttonLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
	buttonLayout.Padding = UDim.new(0, isMobile and 5 or 10)
	buttonLayout.Parent = buttonsFrame

	-- Add padding to buttons frame
	local buttonsPadding = Instance.new("UIPadding")
	buttonsPadding.PaddingAll = UDim.new(0, 5)
	buttonsPadding.Parent = buttonsFrame

	-- Button data for easy creation
	local buttonData = {
		{
			name = "CraftingMenuButton",
			text = "🏭 Craft",
			color = Color3.new(0.6, 0.4, 0.2),
			order = 1
		},
		{
			name = "BuildingMenuButton",
			text = "🏗️ Build",
			color = Color3.new(0.2, 0.6, 0.4),
			order = 2
		},
		{
			name = "PlotMenuButton",
			text = "🏘️ Plots",
			color = Color3.new(0.6, 0.2, 0.6),
			order = 3
		}
	}

	-- Create action buttons with responsive sizing
	for _, data in ipairs(buttonData) do
		local button = Instance.new("TextButton")
		button.Name = data.name

		-- Responsive button sizing
		local buttonWidth = isMobile and 85 or (isTablet and 95 or 100)
		local buttonHeight = isMobile and 35 or 40
		button.Size = UDim2.new(0, buttonWidth, 0, buttonHeight)

		button.BackgroundColor3 = data.color
		button.Text = data.text
		button.TextColor3 = Color3.new(1, 1, 1)
		button.TextScaled = true
		button.Font = Enum.Font.SourceSansBold
		button.LayoutOrder = data.order
		button.Parent = buttonsFrame

		-- Add size constraints for buttons
		local buttonSizeConstraint = Instance.new("UISizeConstraint")
		buttonSizeConstraint.MinSize = Vector2.new(80, 30)
		buttonSizeConstraint.MaxSize = Vector2.new(120, 50)
		buttonSizeConstraint.Parent = button

		-- Add corner radius
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(0, 6)
		corner.Parent = button

		-- Add hover effects
		button.MouseEnter:Connect(function()
			TweenService:Create(button, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(
					math.min(1, data.color.R + 0.1),
					math.min(1, data.color.G + 0.1),
					math.min(1, data.color.B + 0.1)
				)
			}):Play()
		end)

		button.MouseLeave:Connect(function()
			TweenService:Create(button, TweenInfo.new(0.2), {
				BackgroundColor3 = data.color
			}):Play()
		end)
	end

	-- Close button (positioned separately)
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -35, 0, 5)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
	closeButton.Text = "✕"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = statisticsFrame

	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton

	-- Close button hover effect
	closeButton.MouseEnter:Connect(function()
		TweenService:Create(closeButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.9, 0.4, 0.4)
		}):Play()
	end)

	closeButton.MouseLeave:Connect(function()
		TweenService:Create(closeButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
		}):Play()
	end)

	-- Button functionality using the new button references
	local craftingMenuButton = buttonsFrame:FindFirstChild("CraftingMenuButton")
	local buildingMenuButton = buttonsFrame:FindFirstChild("BuildingMenuButton")
	local plotMenuButton = buttonsFrame:FindFirstChild("PlotMenuButton")

	if craftingMenuButton then
		craftingMenuButton.MouseButton1Click:Connect(function()
			-- Trigger crafting UI
			local CraftingUI = require(script.Parent:WaitForChild("CraftingUI"))
			CraftingUI.ShowCraftingWindow()
		end)
	end

	if buildingMenuButton then
		buildingMenuButton.MouseButton1Click:Connect(function()
			-- Trigger building UI
			local BuildingUI = require(script.Parent:WaitForChild("BuildingUI"))
			BuildingUI.OpenBuildingWindow()
		end)
	end

	if plotMenuButton then
		plotMenuButton.MouseButton1Click:Connect(function()
			-- Trigger plot UI
			local PlotUI = require(script.Parent:WaitForChild("PlotUI"))
			PlotUI.OpenPlotWindow()
		end)
	end

	closeButton.MouseButton1Click:Connect(function()
		StatisticsUI.CloseStatistics()
	end)

	print("📊 Statistics frame created!")
end

-- Toggle statistics frame
function StatisticsUI.ToggleStatistics()
	if isOpen then
		StatisticsUI.CloseStatistics()
	else
		StatisticsUI.OpenStatistics()
	end
end

-- Open statistics frame
function StatisticsUI.OpenStatistics()
	if not statisticsFrame then
		StatisticsUI.CreateStatisticsFrame()
	end

	isOpen = true
	statisticsFrame.Visible = true
	statisticsButton.Text = "📊 Hide Stats"

	-- Update statistics data
	StatisticsUI.UpdateStatistics()

	-- Animate in
	statisticsFrame.Position = UDim2.new(0, 0, 0, -80)
	TweenService:Create(statisticsFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
		Position = UDim2.new(0, 0, 0, 0)
	}):Play()

	print("📊 Statistics opened")
end

-- Close statistics frame
function StatisticsUI.CloseStatistics()
	if not statisticsFrame then return end

	isOpen = false
	statisticsButton.Text = "📊 Statistics"

	-- Animate out
	TweenService:Create(statisticsFrame, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.In), {
		Position = UDim2.new(0, 0, 0, -80)
	}).Completed:Connect(function()
		statisticsFrame.Visible = false
	end)

	print("📊 Statistics closed")
end

-- Update statistics data
function StatisticsUI.UpdateStatistics()
	if not statisticsFrame or not statisticsFrame.Visible then return end

	-- Update currency displays using leaderstats
	local leaderstats = player:FindFirstChild("leaderstats")
	if leaderstats then
		local currencyContainer = statisticsFrame:FindFirstChild("CurrencyContainer")
		if currencyContainer then
			local currencies = {"Pieces", "Cash", "XP", "Population", "Level", "Energy", "Water"}
			for _, currency in ipairs(currencies) do
				local currencyFrame = currencyContainer:FindFirstChild(currency .. "Frame")
				if currencyFrame then
					local label = currencyFrame:FindFirstChild(currency .. "Label")
					local leaderstatValue = leaderstats:FindFirstChild(currency)

					if label and leaderstatValue then
						-- Format large numbers
						local value = leaderstatValue.Value
						local formattedValue = value

						if value >= 1000000 then
							formattedValue = string.format("%.1fM", value / 1000000)
						elseif value >= 1000 then
							formattedValue = string.format("%.1fK", value / 1000)
						end

						label.Text = formattedValue
					end
				end
			end
		end
	end
end

-- Initialize statistics system
function StatisticsUI.Initialize()
	print("📊 Initializing Statistics UI...")

	-- Create statistics button
	StatisticsUI.CreateStatisticsButton()

	-- Handle keyboard shortcut
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end

		-- Toggle statistics with Tab key
		if input.KeyCode == Enum.KeyCode.Tab then
			StatisticsUI.ToggleStatistics()
		end
	end)

	-- Monitor leaderstats changes
	local leaderstats = player:WaitForChild("leaderstats", 10)
	if leaderstats then
		local currencies = {"Pieces", "Cash", "XP", "Population", "Level", "Energy", "Water"}
		for _, currency in ipairs(currencies) do
			local leaderstatValue = leaderstats:FindFirstChild(currency)
			if leaderstatValue then
				leaderstatValue.Changed:Connect(function()
					StatisticsUI.UpdateStatistics()
				end)
			end
		end
	end

	print("✅ Statistics UI initialized!")
end

return StatisticsUI
