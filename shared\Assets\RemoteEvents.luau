--[[
	RemoteEvents for UrbanSim
	All client-server communication events
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local RemoteEvents = {}

-- Ensure Assets folder exists
local Assets = ReplicatedStorage:FindFirstChild("Assets")
if not Assets then
	Assets = Instance.new("Folder")
	Assets.Name = "Assets"
	Assets.Parent = ReplicatedStorage
end

-- Create RemoteEvents (singleton pattern to prevent duplicates)
local function createRemoteEvent(name)
	-- Check if RemoteEvent already exists
	local existingEvent = Assets:FindFirstChild(name)
	if existingEvent and existingEvent:IsA("RemoteEvent") then
		print("📡 Using existing RemoteEvent:", name)
		return existingEvent
	end

	-- Create new RemoteEvent if it doesn't exist
	local event = Instance.new("RemoteEvent")
	event.Name = name
	event.Parent = Assets
	print("📡 Created new RemoteEvent:", name)
	return event
end

-- Building System Events
RemoteEvents.PlaceBuilding = createRemoteEvent("PlaceBuilding")
RemoteEvents.UpgradeBuilding = createRemoteEvent("UpgradeBuilding")
RemoteEvents.SellBuilding = createRemoteEvent("SellBuilding")
RemoteEvents.RemoveBuilding = createRemoteEvent("RemoveBuilding")
RemoteEvents.BuildingPlaced = createRemoteEvent("BuildingPlaced")
RemoteEvents.BuildingUpgraded = createRemoteEvent("BuildingUpgraded")
RemoteEvents.BuildingRemoved = createRemoteEvent("BuildingRemoved")
RemoteEvents.StartBuildingPlacement = createRemoteEvent("StartBuildingPlacement")
RemoteEvents.StartBuildingRemoval = createRemoteEvent("StartBuildingRemoval")

-- Plot System Events
RemoteEvents.TeleportToPlot = createRemoteEvent("TeleportToPlot")
RemoteEvents.GetPlotInfo = createRemoteEvent("GetPlotInfo")
RemoteEvents.ClaimPlot = createRemoteEvent("ClaimPlot")
RemoteEvents.ReleasePlot = createRemoteEvent("ReleasePlot")
RemoteEvents.PlotClaimed = createRemoteEvent("PlotClaimed")
RemoteEvents.PlotReleased = createRemoteEvent("PlotReleased")

-- Resource System Events
RemoteEvents.CollectResource = createRemoteEvent("CollectResource")
RemoteEvents.StartCrafting = createRemoteEvent("StartCrafting")
RemoteEvents.CompleteCrafting = createRemoteEvent("CompleteCrafting")
RemoteEvents.CancelCrafting = createRemoteEvent("CancelCrafting")
RemoteEvents.SpeedUpCrafting = createRemoteEvent("SpeedUpCrafting")
RemoteEvents.ResourceUpdated = createRemoteEvent("ResourceUpdated")
RemoteEvents.CraftingStarted = createRemoteEvent("CraftingStarted")
RemoteEvents.CraftingCompleted = createRemoteEvent("CraftingCompleted")
RemoteEvents.CraftingCancelled = createRemoteEvent("CraftingCancelled")
RemoteEvents.CraftingProgress = createRemoteEvent("CraftingProgress")

-- Economy Events
RemoteEvents.CollectTaxes = createRemoteEvent("CollectTaxes")
RemoteEvents.PurchaseWithCash = createRemoteEvent("PurchaseWithCash")
RemoteEvents.CurrencyUpdated = createRemoteEvent("CurrencyUpdated")

-- UI Events
RemoteEvents.OpenBuildingMenu = createRemoteEvent("OpenBuildingMenu")
RemoteEvents.OpenUpgradeMenu = createRemoteEvent("OpenUpgradeMenu")
RemoteEvents.UpdateUI = createRemoteEvent("UpdateUI")
RemoteEvents.ShowNotification = createRemoteEvent("ShowNotification")

-- Mission Events
RemoteEvents.StartMission = createRemoteEvent("StartMission")
RemoteEvents.CompleteMission = createRemoteEvent("CompleteMission")
RemoteEvents.DisasterOccurred = createRemoteEvent("DisasterOccurred")
RemoteEvents.RepairBuilding = createRemoteEvent("RepairBuilding")

-- Zone Expansion Events
RemoteEvents.ExpandZone = createRemoteEvent("ExpandZone")
RemoteEvents.ZoneExpanded = createRemoteEvent("ZoneExpanded")

-- Clan System Events
RemoteEvents.CreateClan = createRemoteEvent("CreateClan")
RemoteEvents.JoinClan = createRemoteEvent("JoinClan")
RemoteEvents.LeaveClan = createRemoteEvent("LeaveClan")
RemoteEvents.ClanWarStart = createRemoteEvent("ClanWarStart")

-- Marketplace Events
RemoteEvents.CreateListing = createRemoteEvent("CreateListing")
RemoteEvents.PurchaseListing = createRemoteEvent("PurchaseListing")
RemoteEvents.MarketplaceUpdated = createRemoteEvent("MarketplaceUpdated")

-- Dialogue Events
RemoteEvents.InteractWithResident = createRemoteEvent("InteractWithResident")
RemoteEvents.DialogueResponse = createRemoteEvent("DialogueResponse")

-- Daily Rewards Events
RemoteEvents.ClaimDailyReward = createRemoteEvent("ClaimDailyReward")
RemoteEvents.ClaimMinuteReward = createRemoteEvent("ClaimMinuteReward")
RemoteEvents.DailyRewardAvailable = createRemoteEvent("DailyRewardAvailable")
RemoteEvents.DailyRewardClaimed = createRemoteEvent("DailyRewardClaimed")
RemoteEvents.MinuteRewardAvailable = createRemoteEvent("MinuteRewardAvailable")
RemoteEvents.MinuteRewardClaimed = createRemoteEvent("MinuteRewardClaimed")
RemoteEvents.DailyStreakUpdate = createRemoteEvent("DailyStreakUpdate")

-- Achievement Events
RemoteEvents.AchievementUnlocked = createRemoteEvent("AchievementUnlocked")
RemoteEvents.AchievementDataUpdate = createRemoteEvent("AchievementDataUpdate")

-- Multiplayer Events
RemoteEvents.GetServerStats = createRemoteEvent("GetServerStats")
RemoteEvents.ServerStatsUpdate = createRemoteEvent("ServerStatsUpdate")
RemoteEvents.GetGlobalLeaderboard = createRemoteEvent("GetGlobalLeaderboard")
RemoteEvents.GlobalLeaderboardUpdate = createRemoteEvent("GlobalLeaderboardUpdate")
RemoteEvents.PlayerActivityUpdate = createRemoteEvent("PlayerActivityUpdate")

-- Gamepass Events
RemoteEvents.PurchaseGamepass = createRemoteEvent("PurchaseGamepass")
RemoteEvents.GamepassDataUpdate = createRemoteEvent("GamepassDataUpdate")
RemoteEvents.RefreshGamepasses = createRemoteEvent("RefreshGamepasses")

return RemoteEvents
