--[[
	Achievement Manager
	Server-side achievement tracking and rewards
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local AchievementSystem = require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("AchievementSystem"))
local DataManager = require(script.Parent:Wait<PERSON><PERSON><PERSON>hil<PERSON>("DataManager"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))

local AchievementManager = {}

-- Track achievement for player
function AchievementManager.TrackAchievement(player, statType, value)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	-- Update stats
	AchievementSystem.UpdateStats(playerData, statType, value)
	
	-- Check for newly unlocked achievements
	local newlyUnlocked = {}
	
	for achievementId, achievement in pairs(AchievementSystem.ACHIEVEMENTS) do
		if AchievementSystem.CheckAchievement(achievementId, playerData) then
			-- Unlock achievement
			local unlockedAchievement = AchievementSystem.UnlockAchievement(playerData, achievementId)
			if unlockedAchievement then
				table.insert(newlyUnlocked, unlockedAchievement)
				
				-- Give rewards
				AchievementManager.GiveAchievementReward(player, unlockedAchievement)
			end
		end
	end
	
	-- Notify client of new achievements
	if #newlyUnlocked > 0 then
		for _, achievement in ipairs(newlyUnlocked) do
			RemoteEvents.AchievementUnlocked:FireClient(player, achievement)
		end
	end
	
	return newlyUnlocked
end

-- Give achievement reward
function AchievementManager.GiveAchievementReward(player, achievement)
	if not achievement.Reward then return end
	
	for currency, amount in pairs(achievement.Reward) do
		if type(amount) == "number" then
			DataManager.AddToPlayer(player, currency, amount)
		end
	end
	
	-- Notify about rewards
	RemoteEvents.ShowNotification:FireClient(player, "Success", 
		"🏆 Achievement Unlocked: " .. achievement.Name .. "!")
end

-- Get player achievement data
function AchievementManager.GetPlayerAchievements(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return {} end
	
	local availableAchievements = AchievementSystem.GetAvailableAchievements(playerData)
	local unlockedCount = 0
	
	if playerData.UnlockedAchievements then
		for _ in pairs(playerData.UnlockedAchievements) do
			unlockedCount = unlockedCount + 1
		end
	end
	
	local totalCount = 0
	for _ in pairs(AchievementSystem.ACHIEVEMENTS) do
		totalCount = totalCount + 1
	end
	
	return {
		Available = availableAchievements,
		UnlockedCount = unlockedCount,
		TotalCount = totalCount,
		Score = AchievementSystem.GetAchievementScore(playerData),
		ByRarity = AchievementSystem.GetUnlockedByRarity(playerData)
	}
end

-- Initialize achievements for new player
function AchievementManager.InitializePlayer(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	-- Initialize achievement data if not exists
	if not playerData.UnlockedAchievements then
		playerData.UnlockedAchievements = {}
	end
	
	if not playerData.AchievementStats then
		playerData.AchievementStats = {}
	end
	
	-- Send initial achievement data to client
	local achievementData = AchievementManager.GetPlayerAchievements(player)
	RemoteEvents.AchievementDataUpdate:FireClient(player, achievementData)
end

-- Track building placement
function AchievementManager.OnBuildingPlaced(player, buildingType, position)
	AchievementManager.TrackAchievement(player, "BuildingPlaced")
end

-- Track crafting completion
function AchievementManager.OnItemCrafted(player, recipe, quantity)
	AchievementManager.TrackAchievement(player, "ItemCrafted", quantity)
end

-- Track pieces earned
function AchievementManager.OnPiecesEarned(player, amount)
	AchievementManager.TrackAchievement(player, "PiecesEarned", amount)
end

-- Track mission completion
function AchievementManager.OnMissionCompleted(player)
	AchievementManager.TrackAchievement(player, "MissionCompleted")
end

-- Track perfect crafting
function AchievementManager.OnPerfectCraft(player)
	AchievementManager.TrackAchievement(player, "PerfectCraft")
end

-- Track craft cancellation
function AchievementManager.OnCraftCanceled(player)
	AchievementManager.TrackAchievement(player, "CraftCanceled")
end

-- Remote function handlers
task.spawn(function()
	local AssetsFolder = ReplicatedStorage:WaitForChild("Assets")
	local RemoteFunctions = require(AssetsFolder:WaitForChild("RemoteFunctions"))
	
	RemoteFunctions.GetPlayerAchievements.OnServerInvoke = function(player)
		return AchievementManager.GetPlayerAchievements(player)
	end
end)

-- Connect to other systems
-- Building system
RemoteEvents.BuildingPlaced.OnServerEvent:Connect(function(player, buildingType, position)
	AchievementManager.OnBuildingPlaced(player, buildingType, position)
end)

-- Crafting system
RemoteEvents.CraftingCompleted.OnServerEvent:Connect(function(player, recipe, quantity)
	AchievementManager.OnItemCrafted(player, recipe, quantity)
end)

RemoteEvents.CraftingCancelled.OnServerEvent:Connect(function(player, slotId)
	AchievementManager.OnCraftCanceled(player)
end)

-- Resource system (pieces earned)
RemoteEvents.ResourceUpdated.OnServerEvent:Connect(function(player, resources)
	-- Track when pieces increase
	local playerData = DataManager.GetPlayerData(player)
	if playerData and playerData.LastPieces then
		local piecesGained = (resources.Pieces or 0) - playerData.LastPieces
		if piecesGained > 0 then
			AchievementManager.OnPiecesEarned(player, piecesGained)
		end
	end
	
	if playerData then
		playerData.LastPieces = resources.Pieces or 0
	end
end)

-- Player events
game.Players.PlayerAdded:Connect(function(player)
	-- Wait for data to load
	task.wait(3)
	AchievementManager.InitializePlayer(player)
end)

return AchievementManager
