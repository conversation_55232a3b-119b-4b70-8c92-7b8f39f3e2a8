--[[
	Multiplayer Manager
	Advanced multiplayer features and social systems
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MessagingService = game:GetService("MessagingService")
local TeleportService = game:GetService("TeleportService")

local DataManager = require(script.Parent:WaitFor<PERSON>hild("DataManager"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))

local MultiplayerManager = {}

-- Server state
local serverPlayers = {}
local globalLeaderboard = {}
local serverStats = {
	StartTime = tick(),
	TotalBuildings = 0,
	TotalPopulation = 0,
	ActivePlayers = 0
}

-- Player session tracking
local playerSessions = {}

-- Initialize multiplayer systems
function MultiplayerManager.Initialize()
	print("🌐 Initializing Multiplayer Manager...")
	
	-- Setup player tracking
	Players.PlayerAdded:Connect(MultiplayerManager.OnPlayerJoined)
	Players.PlayerRemoving:Connect(MultiplayerManager.OnPlayerLeft)
	
	-- Setup periodic updates
	task.spawn(MultiplayerManager.UpdateLoop)
	
	-- Setup cross-server messaging
	MultiplayerManager.SetupMessaging()
	
	print("✅ Multiplayer Manager initialized!")
end

-- Handle player joining
function MultiplayerManager.OnPlayerJoined(player)
	print("👋 Player joined:", player.Name)
	
	-- Initialize player session
	playerSessions[player] = {
		JoinTime = tick(),
		BuildingsPlaced = 0,
		ItemsCrafted = 0,
		PiecesEarned = 0,
		LastActivity = tick()
	}
	
	-- Add to server players
	serverPlayers[player.UserId] = {
		Player = player,
		JoinTime = tick(),
		IsActive = true
	}
	
	-- Update server stats
	serverStats.ActivePlayers = #Players:GetPlayers()
	
	-- Send welcome message
	task.wait(2) -- Wait for UI to load
	local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
	if showNotificationEvent then
		showNotificationEvent:FireClient(player, "Info",
			"🌟 Welcome to UrbanSim! " .. serverStats.ActivePlayers .. " players online.")
	end
	
	-- Send server stats
	MultiplayerManager.SendServerStats(player)
end

-- Handle player leaving
function MultiplayerManager.OnPlayerLeft(player)
	print("👋 Player left:", player.Name)
	
	-- Clean up session data
	if playerSessions[player] then
		local session = playerSessions[player]
		local sessionTime = tick() - session.JoinTime
		
		print("📊 Session stats for " .. player.Name .. ":")
		print("  Time played: " .. math.floor(sessionTime) .. " seconds")
		print("  Buildings placed: " .. session.BuildingsPlaced)
		print("  Items crafted: " .. session.ItemsCrafted)
		
		playerSessions[player] = nil
	end
	
	-- Remove from server players
	serverPlayers[player.UserId] = nil
	
	-- Update server stats
	serverStats.ActivePlayers = #Players:GetPlayers()
end

-- Update player activity
function MultiplayerManager.UpdatePlayerActivity(player, activityType, data)
	local session = playerSessions[player]
	if not session then return end
	
	session.LastActivity = tick()
	
	if activityType == "BuildingPlaced" then
		session.BuildingsPlaced = session.BuildingsPlaced + 1
		serverStats.TotalBuildings = serverStats.TotalBuildings + 1
		
	elseif activityType == "ItemCrafted" then
		session.ItemsCrafted = session.ItemsCrafted + (data.quantity or 1)
		
	elseif activityType == "PiecesEarned" then
		session.PiecesEarned = session.PiecesEarned + (data.amount or 0)
	end
end

-- Get server statistics
function MultiplayerManager.GetServerStats()
	local totalPopulation = 0
	
	-- Calculate total population across all players
	for _, playerData in pairs(serverPlayers) do
		if playerData.Player and playerData.Player.Parent then
			local data = DataManager.GetPlayerData(playerData.Player)
			if data then
				totalPopulation = totalPopulation + (data.Population or 0)
			end
		end
	end
	
	serverStats.TotalPopulation = totalPopulation
	serverStats.Uptime = tick() - serverStats.StartTime
	
	return serverStats
end

-- Send server stats to player
function MultiplayerManager.SendServerStats(player)
	local stats = MultiplayerManager.GetServerStats()
	RemoteEvents.ServerStatsUpdate:FireClient(player, stats)
end

-- Get global leaderboard
function MultiplayerManager.GetGlobalLeaderboard()
	local leaderboard = {}
	
	for _, playerData in pairs(serverPlayers) do
		if playerData.Player and playerData.Player.Parent then
			local data = DataManager.GetPlayerData(playerData.Player)
			if data then
				table.insert(leaderboard, {
					Name = playerData.Player.Name,
					UserId = playerData.Player.UserId,
					Population = data.Population or 0,
					Pieces = data.Pieces or 0,
					XP = data.XP or 0,
					Buildings = #(data.Buildings or {}),
					JoinTime = playerData.JoinTime
				})
			end
		end
	end
	
	-- Sort by population (primary) and XP (secondary)
	table.sort(leaderboard, function(a, b)
		if a.Population == b.Population then
			return a.XP > b.XP
		end
		return a.Population > b.Population
	end)
	
	return leaderboard
end

-- Setup cross-server messaging
function MultiplayerManager.SetupMessaging()
	-- Listen for global events
	local success, connection = pcall(function()
		return MessagingService:SubscribeAsync("UrbanSimGlobal", function(message)
			MultiplayerManager.HandleGlobalMessage(message.Data)
		end)
	end)
	
	if success then
		print("📡 Connected to global messaging system")
	else
		warn("❌ Failed to connect to global messaging:", connection)
	end
end

-- Handle global messages
function MultiplayerManager.HandleGlobalMessage(data)
	if data.Type == "ServerAnnouncement" then
		-- Broadcast announcement to all players
		local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
		if showNotificationEvent then
			for _, player in pairs(Players:GetPlayers()) do
				showNotificationEvent:FireClient(player, "Info", "📢 " .. data.Message)
			end
		end
		
	elseif data.Type == "GlobalLeaderboardUpdate" then
		-- Update global leaderboard cache
		globalLeaderboard = data.Leaderboard or {}
	end
end

-- Send global message
function MultiplayerManager.SendGlobalMessage(messageType, data)
	local success, result = pcall(function()
		MessagingService:PublishAsync("UrbanSimGlobal", {
			Type = messageType,
			ServerId = game.JobId,
			Timestamp = tick(),
			Data = data
		})
	end)
	
	if not success then
		warn("Failed to send global message:", result)
	end
end

-- Get player comparison data
function MultiplayerManager.GetPlayerComparison(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return nil end
	
	local leaderboard = MultiplayerManager.GetGlobalLeaderboard()
	local playerRank = 0
	
	-- Find player's rank
	for i, entry in ipairs(leaderboard) do
		if entry.UserId == player.UserId then
			playerRank = i
			break
		end
	end
	
	return {
		Rank = playerRank,
		TotalPlayers = #leaderboard,
		Population = playerData.Population or 0,
		Pieces = playerData.Pieces or 0,
		XP = playerData.XP or 0,
		Buildings = #(playerData.Buildings or {})
	}
end

-- Update loop
function MultiplayerManager.UpdateLoop()
	while true do
		task.wait(30) -- Update every 30 seconds
		
		-- Update server stats for all players
		for _, player in pairs(Players:GetPlayers()) do
			MultiplayerManager.SendServerStats(player)
		end
		
		-- Send global leaderboard update
		local leaderboard = MultiplayerManager.GetGlobalLeaderboard()
		if #leaderboard > 0 then
			MultiplayerManager.SendGlobalMessage("GlobalLeaderboardUpdate", {
				Leaderboard = leaderboard,
				ServerId = game.JobId
			})
		end
		
		-- Clean up inactive sessions
		MultiplayerManager.CleanupInactiveSessions()
	end
end

-- Clean up inactive sessions
function MultiplayerManager.CleanupInactiveSessions()
	local currentTime = tick()
	local inactiveThreshold = 300 -- 5 minutes
	
	for player, session in pairs(playerSessions) do
		if currentTime - session.LastActivity > inactiveThreshold then
			if player and player.Parent then
				-- Player is AFK
				local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
				if showNotificationEvent then
					showNotificationEvent:FireClient(player, "Warning",
						"⚠️ You've been inactive for 5 minutes. Move around to stay active!")
				end
			end
		end
	end
end

-- Remote event handlers
RemoteEvents.GetServerStats.OnServerEvent:Connect(function(player)
	MultiplayerManager.SendServerStats(player)
end)

RemoteEvents.GetGlobalLeaderboard.OnServerEvent:Connect(function(player)
	local leaderboard = MultiplayerManager.GetGlobalLeaderboard()
	RemoteEvents.GlobalLeaderboardUpdate:FireClient(player, leaderboard)
end)

-- Track player activities
RemoteEvents.BuildingPlaced.OnServerEvent:Connect(function(player, buildingType, position)
	MultiplayerManager.UpdatePlayerActivity(player, "BuildingPlaced", {
		buildingType = buildingType,
		position = position
	})
end)

RemoteEvents.CraftingCompleted.OnServerEvent:Connect(function(player, recipe, quantity)
	MultiplayerManager.UpdatePlayerActivity(player, "ItemCrafted", {
		recipe = recipe,
		quantity = quantity
	})
end)

-- Initialize on module load
MultiplayerManager.Initialize()

return MultiplayerManager
