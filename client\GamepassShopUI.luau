--[[
	Gamepass Shop UI
	Beautiful animated gamepass shop with marketplace integration
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local MarketplaceService = game:GetService("MarketplaceService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Get shared modules
local Shared = ReplicatedStorage:WaitForChild("Shared")
local GamepassSystem = require(Shared:WaitForChild("GamepassSystem"))

-- Get RemoteEvents and RemoteFunctions (as instances)
local Assets = ReplicatedStorage:WaitForChild("Assets")

-- Initialize the modules first
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitForChild("RemoteEvents"))
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitForChild("RemoteFunctions"))

-- Access as instances
local RemoteEvents = {
	PurchaseGamepass = Assets:WaitForChild("PurchaseGamepass")
}

local RemoteFunctions = {
	GetGamepassShop = Assets:WaitForChild("GetGamepassShop")
}

local GamepassShopUI = {}

-- UI State
local shopWindow = nil
local shopButton = nil
local currentCategory = "Featured"
local isOpen = false
local isInitialized = false

-- Animation settings
local TWEEN_INFO = {
	SlideIn = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
	SlideOut = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
	Bounce = TweenInfo.new(0.3, Enum.EasingStyle.Elastic, Enum.EasingDirection.Out),
	Glow = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
}

-- Create shop button in main UI
function GamepassShopUI.CreateShopButton()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end
	
	-- Shop button - positioned below daily rewards with proper spacing
	shopButton = Instance.new("TextButton")
	shopButton.Name = "GamepassShopButton"
	shopButton.Size = UDim2.new(0, 80, 0, 80)
	shopButton.Position = UDim2.new(1, -100, 0, 160) -- Below daily rewards button (70 + 80 + 10 margin)
	shopButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
	shopButton.Text = "🛒"
	shopButton.TextColor3 = Color3.new(1, 1, 1)
	shopButton.TextScaled = true
	shopButton.Font = Enum.Font.SourceSansBold
	shopButton.Parent = screenGui
	
	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = shopButton
	
	-- Add glow effect
	local glow = Instance.new("ImageLabel")
	glow.Name = "Glow"
	glow.Size = UDim2.new(1, 20, 1, 20)
	glow.Position = UDim2.new(0, -10, 0, -10)
	glow.BackgroundTransparency = 1
	glow.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png"
	glow.ImageColor3 = Color3.new(0, 1, 0)
	glow.ImageTransparency = 0.7
	glow.ZIndex = shopButton.ZIndex - 1
	glow.Parent = shopButton
	
	local glowCorner = Instance.new("UICorner")
	glowCorner.CornerRadius = UDim.new(0, 16)
	glowCorner.Parent = glow
	
	-- Button functionality
	shopButton.MouseButton1Click:Connect(function()
		GamepassShopUI.ShowShop()
	end)
	
	-- Hover effects
	shopButton.MouseEnter:Connect(function()
		TweenService:Create(shopButton, TWEEN_INFO.Bounce, {Size = UDim2.new(0, 85, 0, 85)}):Play()
	end)
	
	shopButton.MouseLeave:Connect(function()
		TweenService:Create(shopButton, TWEEN_INFO.Bounce, {Size = UDim2.new(0, 80, 0, 80)}):Play()
	end)
	
	-- Start glow animation
	local glowTween = TweenService:Create(glow, TWEEN_INFO.Glow, {ImageTransparency = 0.3})
	glowTween:Play()
	
	return shopButton
end

-- Create main shop window
function GamepassShopUI.CreateShopWindow()
	if shopWindow then
		shopWindow:Destroy()
	end
	
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end
	
	-- Main window
	shopWindow = Instance.new("Frame")
	shopWindow.Name = "GamepassShopWindow"
	shopWindow.Size = UDim2.new(0, 1000, 0, 700)
	shopWindow.Position = UDim2.new(0.5, -500, 0.5, -350)
	shopWindow.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
	shopWindow.BorderSizePixel = 0
	shopWindow.Visible = false
	shopWindow.Parent = screenGui
	
	-- Add corner radius and gradient
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 16)
	corner.Parent = shopWindow
	
	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.1, 0.05, 0.2)),
		ColorSequenceKeypoint.new(1, Color3.new(0.05, 0.05, 0.1))
	}
	gradient.Rotation = 45
	gradient.Parent = shopWindow
	
	-- Title bar
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 60)
	titleBar.BackgroundColor3 = Color3.new(0.15, 0.1, 0.25)
	titleBar.BorderSizePixel = 0
	titleBar.Parent = shopWindow
	
	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 16)
	titleCorner.Parent = titleBar
	
	-- Title text
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -120, 1, 0)
	titleText.Position = UDim2.new(0, 20, 0, 0)
	titleText.BackgroundTransparency = 1
	titleText.Text = "🛒 Gamepass Shop - Premium Upgrades"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextScaled = true
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextXAlignment = Enum.TextXAlignment.Left
	titleText.Parent = titleBar
	
	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 40, 0, 40)
	closeButton.Position = UDim2.new(1, -50, 0, 10)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = titleBar
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 8)
	closeCorner.Parent = closeButton
	
	-- Close functionality
	closeButton.MouseButton1Click:Connect(function()
		if GamepassShopUI and GamepassShopUI.CloseShop then
			GamepassShopUI.CloseShop()
		else
			warn("🛒 GamepassShopUI.CloseShop not available!")
			-- Fallback: hide window directly
			if shopWindow then
				shopWindow.Visible = false
				isOpen = false
			end
		end
	end)
	
	-- Create category tabs
	GamepassShopUI.CreateCategoryTabs()
	
	-- Create content area
	GamepassShopUI.CreateContentArea()
	
	return shopWindow
end

-- Create category tabs
function GamepassShopUI.CreateCategoryTabs()
	local tabContainer = Instance.new("Frame")
	tabContainer.Name = "TabContainer"
	tabContainer.Size = UDim2.new(1, -20, 0, 50)
	tabContainer.Position = UDim2.new(0, 10, 0, 70)
	tabContainer.BackgroundTransparency = 1
	tabContainer.Parent = shopWindow
	
	-- Tab layout
	local tabLayout = Instance.new("UIListLayout")
	tabLayout.FillDirection = Enum.FillDirection.Horizontal
	tabLayout.SortOrder = Enum.SortOrder.LayoutOrder
	tabLayout.Padding = UDim.new(0, 5)
	tabLayout.Parent = tabContainer
	
	-- Create tabs
	local tabs = {"Featured", "Currency", "Buildings", "Multipliers", "Convenience", "VIP", "Special"}
	local tabIcons = {"⭐", "💰", "🏗️", "📈", "⚡", "👑", "🏆"}
	
	for i, tabName in ipairs(tabs) do
		local tabButton = Instance.new("TextButton")
		tabButton.Name = tabName .. "Tab"
		tabButton.Size = UDim2.new(0, 120, 1, 0)
		tabButton.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
		tabButton.Text = tabIcons[i] .. " " .. tabName
		tabButton.TextColor3 = Color3.new(1, 1, 1)
		tabButton.TextScaled = true
		tabButton.Font = Enum.Font.SourceSansBold
		tabButton.Parent = tabContainer
		
		local buttonCorner = Instance.new("UICorner")
		buttonCorner.CornerRadius = UDim.new(0, 8)
		buttonCorner.Parent = tabButton
		
		-- Tab functionality
		tabButton.MouseButton1Click:Connect(function()
			GamepassShopUI.SwitchCategory(tabName)
		end)
		
		-- Hover effects
		tabButton.MouseEnter:Connect(function()
			TweenService:Create(tabButton, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
			}):Play()
		end)
		
		tabButton.MouseLeave:Connect(function()
			if currentCategory ~= tabName then
				TweenService:Create(tabButton, TweenInfo.new(0.2), {
					BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
				}):Play()
			end
		end)
	end
end

-- Create content area
function GamepassShopUI.CreateContentArea()
	local contentArea = Instance.new("ScrollingFrame")
	contentArea.Name = "ContentArea"
	contentArea.Size = UDim2.new(1, -20, 1, -140)
	contentArea.Position = UDim2.new(0, 10, 0, 130)
	contentArea.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	contentArea.BorderSizePixel = 0
	contentArea.ScrollBarThickness = 8
	contentArea.Parent = shopWindow
	
	local contentCorner = Instance.new("UICorner")
	contentCorner.CornerRadius = UDim.new(0, 12)
	contentCorner.Parent = contentArea
	
	-- Grid layout for gamepass cards
	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellSize = UDim2.new(0, 300, 0, 200)
	gridLayout.CellPadding = UDim2.new(0, 10, 0, 10)
	gridLayout.SortOrder = Enum.SortOrder.LayoutOrder
	gridLayout.Parent = contentArea
	
	return contentArea
end

-- Switch category
function GamepassShopUI.SwitchCategory(categoryName)
	currentCategory = categoryName
	
	-- Update tab appearance
	local tabContainer = shopWindow:FindFirstChild("TabContainer")
	if tabContainer then
		for _, tab in ipairs(tabContainer:GetChildren()) do
			if tab:IsA("TextButton") then
				if tab.Name == categoryName .. "Tab" then
					tab.BackgroundColor3 = Color3.new(0.2, 0.6, 1) -- Active color
				else
					tab.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2) -- Inactive color
				end
			end
		end
	end
	
	-- Load category content
	GamepassShopUI.LoadCategoryContent(categoryName)
end

-- Load category content
function GamepassShopUI.LoadCategoryContent(categoryName)
	local contentArea = shopWindow:FindFirstChild("ContentArea")
	if not contentArea then return end
	
	-- Clear existing content
	for _, child in ipairs(contentArea:GetChildren()) do
		if child:IsA("Frame") then
			child:Destroy()
		end
	end
	
	-- Get shop data with error handling
	local shopData
	local success, result = pcall(function()
		return RemoteFunctions.GetGamepassShop:InvokeServer()
	end)

	if success and result then
		shopData = result
	else
		warn("Failed to get gamepass shop data:", result)
		-- Use fallback data with direct gamepass references
		print("🛒 Using fallback gamepass data")
		shopData = {
			Featured = {"STARTER_PACK", "VIP_STATUS", "DOUBLE_INCOME", "PREMIUM_BUILDINGS"},
			Popular = {"STARTER_PACK", "TRIPLE_XP", "FAST_BUILD", "AUTO_COLLECT"},
			Categories = {
				Currency = {
					Gamepasses = {
						STARTER_PACK = GamepassSystem.GAMEPASSES.STARTER_PACK,
						MEGA_PACK = GamepassSystem.GAMEPASSES.MEGA_PACK
					}
				},
				Buildings = {
					Gamepasses = {
						PREMIUM_BUILDINGS = GamepassSystem.GAMEPASSES.PREMIUM_BUILDINGS,
						INDUSTRIAL_PACK = GamepassSystem.GAMEPASSES.INDUSTRIAL_PACK
					}
				},
				Multipliers = {
					Gamepasses = {
						DOUBLE_INCOME = GamepassSystem.GAMEPASSES.DOUBLE_INCOME,
						TRIPLE_XP = GamepassSystem.GAMEPASSES.TRIPLE_XP,
						FAST_BUILD = GamepassSystem.GAMEPASSES.FAST_BUILD
					}
				},
				Convenience = {
					Gamepasses = {
						UNLIMITED_STORAGE = GamepassSystem.GAMEPASSES.UNLIMITED_STORAGE,
						AUTO_COLLECT = GamepassSystem.GAMEPASSES.AUTO_COLLECT
					}
				},
				VIP = {
					Gamepasses = {
						VIP_STATUS = GamepassSystem.GAMEPASSES.VIP_STATUS
					}
				},
				Special = {
					Gamepasses = {
						GOLDEN_EDITION = GamepassSystem.GAMEPASSES.GOLDEN_EDITION
					}
				}
			}
		}
	end

	local gamepasses = {}

	print("🛒 Loading category:", categoryName)

	if categoryName == "Featured" then
		print("🛒 Loading Featured gamepasses...")
		for _, gamepassKey in ipairs(shopData.Featured or {}) do
			local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
			if gamepass then
				gamepasses[gamepassKey] = gamepass
				print("🛒 Added Featured gamepass:", gamepassKey, gamepass.Name)
			else
				warn("🛒 Missing gamepass:", gamepassKey)
			end
		end
	elseif categoryName == "Popular" then
		print("🛒 Loading Popular gamepasses...")
		for _, gamepassKey in ipairs(shopData.Popular or {}) do
			local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
			if gamepass then
				gamepasses[gamepassKey] = gamepass
				print("🛒 Added Popular gamepass:", gamepassKey, gamepass.Name)
			else
				warn("🛒 Missing gamepass:", gamepassKey)
			end
		end
	else
		print("🛒 Loading category gamepasses for:", categoryName)
		local categoryData = shopData.Categories[categoryName]
		if categoryData and categoryData.Gamepasses then
			gamepasses = categoryData.Gamepasses
			print("🛒 Found", #gamepasses, "gamepasses in category")
		else
			warn("🛒 No data found for category:", categoryName)
		end
	end

	print("🛒 Total gamepasses to display:", #gamepasses)

	-- Create gamepass cards
	local cardCount = 0
	for gamepassKey, gamepass in pairs(gamepasses) do
		if gamepass and gamepass.Name then
			GamepassShopUI.CreateGamepassCard(gamepassKey, gamepass, contentArea)
			cardCount = cardCount + 1
			print("🛒 Created card for:", gamepass.Name)
		else
			warn("🛒 Invalid gamepass data for:", gamepassKey)
		end
	end

	print("🛒 Created", cardCount, "gamepass cards")
	
	-- Update canvas size
	contentArea.CanvasSize = UDim2.new(0, 0, 0, contentArea.UIGridLayout.AbsoluteContentSize.Y + 20)
end

-- Create gamepass card
function GamepassShopUI.CreateGamepassCard(gamepassKey, gamepass, parent)
	local card = Instance.new("Frame")
	card.Name = gamepassKey .. "Card"
	card.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	card.BorderSizePixel = 0
	card.Parent = parent
	
	local cardCorner = Instance.new("UICorner")
	cardCorner.CornerRadius = UDim.new(0, 12)
	cardCorner.Parent = card
	
	-- Gradient background
	local cardGradient = Instance.new("UIGradient")
	cardGradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.2, 0.2, 0.25)),
		ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.1, 0.15))
	}
	cardGradient.Rotation = 45
	cardGradient.Parent = card
	
	-- Icon
	local icon = Instance.new("TextLabel")
	icon.Size = UDim2.new(0, 50, 0, 50)
	icon.Position = UDim2.new(0, 10, 0, 10)
	icon.BackgroundTransparency = 1
	icon.Text = gamepass.Icon or "🎫"
	icon.TextScaled = true
	icon.Font = Enum.Font.SourceSansBold
	icon.Parent = card
	
	-- Name
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Size = UDim2.new(1, -70, 0, 30)
	nameLabel.Position = UDim2.new(0, 70, 0, 10)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = gamepass.Name
	nameLabel.TextColor3 = Color3.new(1, 1, 1)
	nameLabel.TextScaled = true
	nameLabel.Font = Enum.Font.SourceSansBold
	nameLabel.TextXAlignment = Enum.TextXAlignment.Left
	nameLabel.Parent = card
	
	-- Description
	local description = Instance.new("TextLabel")
	description.Size = UDim2.new(1, -20, 0, 60)
	description.Position = UDim2.new(0, 10, 0, 50)
	description.BackgroundTransparency = 1
	description.Text = gamepass.Description
	description.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	description.TextSize = 12
	description.Font = Enum.Font.SourceSans
	description.TextWrapped = true
	description.TextXAlignment = Enum.TextXAlignment.Left
	description.TextYAlignment = Enum.TextYAlignment.Top
	description.Parent = card
	
	-- Price and purchase button
	local purchaseButton = Instance.new("TextButton")
	purchaseButton.Size = UDim2.new(1, -20, 0, 40)
	purchaseButton.Position = UDim2.new(0, 10, 1, -50)
	purchaseButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
	purchaseButton.Text = "🔥 " .. gamepass.Price .. " Robux"
	purchaseButton.TextColor3 = Color3.new(1, 1, 1)
	purchaseButton.TextScaled = true
	purchaseButton.Font = Enum.Font.SourceSansBold
	purchaseButton.Parent = card
	
	local purchaseCorner = Instance.new("UICorner")
	purchaseCorner.CornerRadius = UDim.new(0, 8)
	purchaseCorner.Parent = purchaseButton
	
	-- Purchase functionality
	purchaseButton.MouseButton1Click:Connect(function()
		RemoteEvents.PurchaseGamepass:FireServer(gamepassKey)
	end)
	
	-- Hover effects
	card.MouseEnter:Connect(function()
		TweenService:Create(card, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
		}):Play()
	end)
	
	card.MouseLeave:Connect(function()
		TweenService:Create(card, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
		}):Play()
	end)
	
	return card
end

-- Show shop window
function GamepassShopUI.ShowShop()
	print("🛒 ShowShop called")

	if isOpen then
		print("🛒 Shop already open, returning")
		return
	end
	isOpen = true

	if not shopWindow then
		print("🛒 Creating shop window...")
		GamepassShopUI.CreateShopWindow()
	end

	if not shopWindow then
		print("🛒 ERROR: Failed to create shop window!")
		isOpen = false
		return
	end

	-- Play window open sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_OPEN")
		end
	end)

	print("🛒 Making window visible and animating...")
	shopWindow.Visible = true

	-- Slide in animation
	shopWindow.Position = UDim2.new(0.5, -500, 1, 0)
	TweenService:Create(shopWindow, TWEEN_INFO.SlideIn, {
		Position = UDim2.new(0.5, -500, 0.5, -350)
	}):Play()

	-- Load featured category by default
	print("🛒 Loading Featured category...")
	GamepassShopUI.SwitchCategory("Featured")
	print("🛒 Shop window opened successfully")
end

-- Close shop window
function GamepassShopUI.CloseShop()
	print("🛒 CloseShop called")

	if not isOpen then
		print("🛒 Shop not open")
		return
	end

	if not shopWindow then
		print("🛒 Shop window doesn't exist")
		isOpen = false
		return
	end

	isOpen = false

	-- Play window close sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_CLOSE")
		end
	end)

	print("🛒 Animating shop window close...")
	TweenService:Create(shopWindow, TWEEN_INFO.SlideOut, {
		Position = UDim2.new(0.5, -500, 1, 0)
	}).Completed:Connect(function()
		print("🛒 Shop window hidden")
		shopWindow.Visible = false
	end)
end

-- Initialize gamepass shop UI
function GamepassShopUI.Initialize()
	print("🛒 GamepassShopUI.Initialize() called")

	local success, result = pcall(function()
		GamepassShopUI.CreateShopButton()
		isInitialized = true
		print("🛒 GamepassShopUI initialized successfully")
	end)

	if not success then
		warn("🛒 Failed to initialize GamepassShopUI:", result)
		return
	end

	-- Event handlers
	if RemoteEvents and RemoteEvents.GamepassDataUpdate then
		RemoteEvents.GamepassDataUpdate.OnClientEvent:Connect(function(ownedGamepasses)
			-- Update UI to show owned gamepasses
			if isOpen then
				GamepassShopUI.LoadCategoryContent(currentCategory)
			end
		end)
	end
end

-- Note: Initialize() will be called from main client after UI is created

return GamepassShopUI
