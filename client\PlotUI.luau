-- PlotUI.luau
-- Client-side UI for plot management

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for shared modules
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = {
	TeleportToPlot = Assets:WaitForChild("TeleportToPlot"),
	ShowNotification = Assets:WaitForChild("ShowNotification"),
	ClaimPlot = Assets:WaitForChild("ClaimPlot"),
	ReleasePlot = Assets:WaitForChild("ReleasePlot"),
	PlotClaimed = Assets:WaitForChild("PlotClaimed"),
	PlotReleased = Assets:WaitForChild("PlotReleased")
}
local RemoteFunctions = {
	GetPlayerPlotInfo = Assets:WaitForChild("GetPlayerPlotInfo"),
	GetPlotInfo = Assets:WaitForChild("GetPlotInfo"),
	GetAllPlotsInfo = Assets:WaitForChild("GetAllPlotsInfo")
}

local PlotUI = {}

-- UI State
local plotWindow = nil
local plotBrowserWindow = nil
local isOpen = false
local isBrowserOpen = false
local playerPlotInfo = nil
local allPlotsInfo = nil

-- Create plot management UI
function PlotUI.CreatePlotWindow()
	if plotWindow then
		plotWindow:Destroy()
	end

	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then
		warn("🏘️ UrbanSimUI not found!")
		return
	end

	-- Main plot window with mobile responsiveness
	plotWindow = Instance.new("Frame")
	plotWindow.Name = "PlotWindow"

	-- Responsive sizing for mobile
	local screenSize = workspace.CurrentCamera.ViewportSize
	local isMobile = screenSize.X < 800 or screenSize.Y < 600

	if isMobile then
		plotWindow.Size = UDim2.new(0.9, 0, 0, 350)
		plotWindow.Position = UDim2.new(0.05, 0, 0.5, -175)
	else
		plotWindow.Size = UDim2.new(0, 400, 0, 330)
		plotWindow.Position = UDim2.new(0.5, -200, 0.5, -165)
	end

	plotWindow.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	plotWindow.BackgroundTransparency = 0 -- Ensure visibility
	plotWindow.BorderSizePixel = 0
	plotWindow.Visible = false
	plotWindow.ZIndex = 10 -- Ensure it's above other elements
	plotWindow.Parent = screenGui

	-- Add corner radius
	local windowCorner = Instance.new("UICorner")
	windowCorner.CornerRadius = UDim.new(0, 12)
	windowCorner.Parent = plotWindow

	-- Add shadow
	local shadow = Instance.new("Frame")
	shadow.Name = "Shadow"
	shadow.Size = UDim2.new(1, 6, 1, 6)
	shadow.Position = UDim2.new(0, -3, 0, 3)
	shadow.BackgroundColor3 = Color3.new(0, 0, 0)
	shadow.BackgroundTransparency = 0.7
	shadow.ZIndex = plotWindow.ZIndex - 1
	shadow.Parent = plotWindow

	local shadowCorner = Instance.new("UICorner")
	shadowCorner.CornerRadius = UDim.new(0, 12)
	shadowCorner.Parent = shadow

	-- Title bar
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 50)
	titleBar.Position = UDim2.new(0, 0, 0, 0)
	titleBar.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	titleBar.BorderSizePixel = 0
	titleBar.Parent = plotWindow

	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 12)
	titleCorner.Parent = titleBar

	-- Title text
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -100, 1, 0)
	titleText.Position = UDim2.new(0, 15, 0, 0)
	titleText.BackgroundTransparency = 1
	titleText.Text = "🏘️ My Plot"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextSize = 20
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextXAlignment = Enum.TextXAlignment.Left
	titleText.Parent = titleBar

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -40, 0, 10)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextSize = 18
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = titleBar

	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton

	-- Plot info section
	local infoSection = Instance.new("Frame")
	infoSection.Name = "InfoSection"
	infoSection.Size = UDim2.new(1, -20, 0, 120)
	infoSection.Position = UDim2.new(0, 10, 0, 60)
	infoSection.BackgroundColor3 = Color3.new(0.12, 0.12, 0.17)
	infoSection.BorderSizePixel = 0
	infoSection.Parent = plotWindow

	local infoCorner = Instance.new("UICorner")
	infoCorner.CornerRadius = UDim.new(0, 8)
	infoCorner.Parent = infoSection

	-- Plot number label
	local plotNumberLabel = Instance.new("TextLabel")
	plotNumberLabel.Name = "PlotNumber"
	plotNumberLabel.Size = UDim2.new(1, -20, 0, 30)
	plotNumberLabel.Position = UDim2.new(0, 10, 0, 10)
	plotNumberLabel.BackgroundTransparency = 1
	plotNumberLabel.Text = "🏘️ Plot: Loading..."
	plotNumberLabel.TextColor3 = Color3.new(1, 1, 1)
	plotNumberLabel.TextSize = 16
	plotNumberLabel.Font = Enum.Font.SourceSansBold
	plotNumberLabel.TextXAlignment = Enum.TextXAlignment.Left
	plotNumberLabel.Parent = infoSection

	-- Location label
	local locationLabel = Instance.new("TextLabel")
	locationLabel.Name = "Location"
	locationLabel.Size = UDim2.new(1, -20, 0, 25)
	locationLabel.Position = UDim2.new(0, 10, 0, 40)
	locationLabel.BackgroundTransparency = 1
	locationLabel.Text = "📍 Location: Loading..."
	locationLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	locationLabel.TextSize = 14
	locationLabel.Font = Enum.Font.SourceSans
	locationLabel.TextXAlignment = Enum.TextXAlignment.Left
	locationLabel.Parent = infoSection

	-- Buildings label
	local buildingsLabel = Instance.new("TextLabel")
	buildingsLabel.Name = "Buildings"
	buildingsLabel.Size = UDim2.new(1, -20, 0, 25)
	buildingsLabel.Position = UDim2.new(0, 10, 0, 65)
	buildingsLabel.BackgroundTransparency = 1
	buildingsLabel.Text = "🏗️ Buildings: Loading..."
	buildingsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	buildingsLabel.TextSize = 14
	buildingsLabel.Font = Enum.Font.SourceSans
	buildingsLabel.TextXAlignment = Enum.TextXAlignment.Left
	buildingsLabel.Parent = infoSection

	-- Last active label
	local lastActiveLabel = Instance.new("TextLabel")
	lastActiveLabel.Name = "LastActive"
	lastActiveLabel.Size = UDim2.new(1, -20, 0, 25)
	lastActiveLabel.Position = UDim2.new(0, 10, 0, 90)
	lastActiveLabel.BackgroundTransparency = 1
	lastActiveLabel.Text = "⏰ Last Active: Loading..."
	lastActiveLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	lastActiveLabel.TextSize = 14
	lastActiveLabel.Font = Enum.Font.SourceSans
	lastActiveLabel.TextXAlignment = Enum.TextXAlignment.Left
	lastActiveLabel.Parent = infoSection

	-- Teleport button
	local teleportButton = Instance.new("TextButton")
	teleportButton.Name = "TeleportButton"
	teleportButton.Size = UDim2.new(1, -20, 0, 40)
	teleportButton.Position = UDim2.new(0, 10, 0, 190)
	teleportButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
	teleportButton.Text = "🚀 Teleport to My Plot"
	teleportButton.TextColor3 = Color3.new(1, 1, 1)
	teleportButton.TextSize = 16
	teleportButton.Font = Enum.Font.SourceSansBold
	teleportButton.Parent = plotWindow

	local teleportCorner = Instance.new("UICorner")
	teleportCorner.CornerRadius = UDim.new(0, 8)
	teleportCorner.Parent = teleportButton

	-- Browse plots button
	local browsePlotsButton = Instance.new("TextButton")
	browsePlotsButton.Name = "BrowsePlotsButton"
	browsePlotsButton.Size = UDim2.new(0.48, 0, 0, 35)
	browsePlotsButton.Position = UDim2.new(0, 10, 0, 240)
	browsePlotsButton.BackgroundColor3 = Color3.new(0.4, 0.3, 0.6)
	browsePlotsButton.Text = "🔍 Browse Plots"
	browsePlotsButton.TextColor3 = Color3.new(1, 1, 1)
	browsePlotsButton.TextSize = 14
	browsePlotsButton.Font = Enum.Font.SourceSans
	browsePlotsButton.Parent = plotWindow

	local browseCorner = Instance.new("UICorner")
	browseCorner.CornerRadius = UDim.new(0, 6)
	browseCorner.Parent = browsePlotsButton

	-- Release plot button
	local releasePlotButton = Instance.new("TextButton")
	releasePlotButton.Name = "ReleasePlotButton"
	releasePlotButton.Size = UDim2.new(0.48, 0, 0, 35)
	releasePlotButton.Position = UDim2.new(0.52, 0, 0, 240)
	releasePlotButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
	releasePlotButton.Text = "🗑️ Release Plot"
	releasePlotButton.TextColor3 = Color3.new(1, 1, 1)
	releasePlotButton.TextSize = 14
	releasePlotButton.Font = Enum.Font.SourceSans
	releasePlotButton.Parent = plotWindow

	local releaseCorner = Instance.new("UICorner")
	releaseCorner.CornerRadius = UDim.new(0, 6)
	releaseCorner.Parent = releasePlotButton

	-- Refresh button
	local refreshButton = Instance.new("TextButton")
	refreshButton.Name = "RefreshButton"
	refreshButton.Size = UDim2.new(1, -20, 0, 30)
	refreshButton.Position = UDim2.new(0, 10, 0, 285)
	refreshButton.BackgroundColor3 = Color3.new(0.3, 0.3, 0.4)
	refreshButton.Text = "🔄 Refresh Info"
	refreshButton.TextColor3 = Color3.new(1, 1, 1)
	refreshButton.TextSize = 12
	refreshButton.Font = Enum.Font.SourceSans
	refreshButton.Parent = plotWindow

	local refreshCorner = Instance.new("UICorner")
	refreshCorner.CornerRadius = UDim.new(0, 6)
	refreshCorner.Parent = refreshButton

	-- Button functionality
	closeButton.MouseButton1Click:Connect(function()
		PlotUI.ClosePlotWindow()
	end)

	teleportButton.MouseButton1Click:Connect(function()
		PlotUI.TeleportToPlot()
	end)

	browsePlotsButton.MouseButton1Click:Connect(function()
		PlotUI.OpenPlotBrowser()
	end)

	releasePlotButton.MouseButton1Click:Connect(function()
		PlotUI.ReleasePlot()
	end)

	refreshButton.MouseButton1Click:Connect(function()
		PlotUI.RefreshPlotInfo()
	end)

	-- Hover effects
	local function addHoverEffect(button, hoverColor, normalColor)
		button.MouseEnter:Connect(function()
			TweenService:Create(button, TweenInfo.new(0.2), {
				BackgroundColor3 = hoverColor
			}):Play()
		end)

		button.MouseLeave:Connect(function()
			TweenService:Create(button, TweenInfo.new(0.2), {
				BackgroundColor3 = normalColor
			}):Play()
		end)
	end

	addHoverEffect(closeButton, Color3.new(1, 0.3, 0.3), Color3.new(0.8, 0.2, 0.2))
	addHoverEffect(teleportButton, Color3.new(0.3, 0.7, 0.9), Color3.new(0.2, 0.6, 0.8))
	addHoverEffect(browsePlotsButton, Color3.new(0.5, 0.4, 0.7), Color3.new(0.4, 0.3, 0.6))
	addHoverEffect(releasePlotButton, Color3.new(0.9, 0.4, 0.4), Color3.new(0.8, 0.3, 0.3))
	addHoverEffect(refreshButton, Color3.new(0.4, 0.4, 0.5), Color3.new(0.3, 0.3, 0.4))

	print("🏘️ Plot window created!")
end

-- Open plot window
function PlotUI.OpenPlotWindow()
	if not plotWindow then
		PlotUI.CreatePlotWindow()
	end

	if isOpen then return end

	isOpen = true

	-- Play window open sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_OPEN")
		end
	end)

	plotWindow.Visible = true

	-- Animate window opening
	plotWindow.Size = UDim2.new(0, 0, 0, 0)
	plotWindow.Position = UDim2.new(0.5, 0, 0.5, 0)

	TweenService:Create(plotWindow, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
		Size = UDim2.new(0, 400, 0, 330),
		Position = UDim2.new(0.5, -200, 0.5, -165)
	}):Play()

	-- Load plot info
	PlotUI.RefreshPlotInfo()

	print("🏘️ Plot window opened!")
end

-- Close plot window
function PlotUI.ClosePlotWindow()
	if not isOpen then return end

	isOpen = false

	-- Play window close sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_CLOSE")
		end
	end)

	-- Animate window closing
	TweenService:Create(plotWindow, TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In), {
		Size = UDim2.new(0, 0, 0, 0),
		Position = UDim2.new(0.5, 0, 0.5, 0)
	}).Completed:Connect(function()
		plotWindow.Visible = false
	end)

	print("🏘️ Plot window closed!")
end

-- Refresh plot info
function PlotUI.RefreshPlotInfo()
	if not plotWindow then return end

	local success, result = pcall(function()
		return RemoteFunctions.GetPlayerPlotInfo:InvokeServer()
	end)

	if success and result then
		playerPlotInfo = result

		-- Update UI elements
		local plotNumberLabel = plotWindow.InfoSection:FindFirstChild("PlotNumber")
		local locationLabel = plotWindow.InfoSection:FindFirstChild("Location")
		local buildingsLabel = plotWindow.InfoSection:FindFirstChild("Buildings")
		local lastActiveLabel = plotWindow.InfoSection:FindFirstChild("LastActive")

		if plotNumberLabel then
			plotNumberLabel.Text = "🏘️ Plot: " .. result.PlotNumber
		end

		if locationLabel then
			locationLabel.Text = "📍 Location: " .. math.floor(result.Position.X) .. ", " .. math.floor(result.Position.Z)
		end

		if buildingsLabel then
			buildingsLabel.Text = "🏗️ Buildings: " .. result.Buildings
		end

		if lastActiveLabel then
			local timeAgo = tick() - result.LastActive
			local timeText = ""
			if timeAgo < 60 then
				timeText = "Just now"
			elseif timeAgo < 3600 then
				timeText = math.floor(timeAgo / 60) .. " minutes ago"
			else
				timeText = math.floor(timeAgo / 3600) .. " hours ago"
			end
			lastActiveLabel.Text = "⏰ Last Active: " .. timeText
		end

		print("🏘️ Plot info refreshed!")
	else
		warn("🏘️ Failed to get plot info:", result)
		
		-- Show error in UI
		local plotNumberLabel = plotWindow.InfoSection:FindFirstChild("PlotNumber")
		if plotNumberLabel then
			plotNumberLabel.Text = "🏘️ Plot: No plot assigned"
		end
	end
end

-- Teleport to plot
function PlotUI.TeleportToPlot()
	if not playerPlotInfo then
		RemoteEvents.ShowNotification:FireClient(player, "Error", "No plot info available!")
		return
	end

	RemoteEvents.TeleportToPlot:FireServer()
	PlotUI.ClosePlotWindow()
end

-- Release plot with confirmation
function PlotUI.ReleasePlot()
	if not playerPlotInfo then
		RemoteEvents.ShowNotification:FireClient(player, "Error", "No plot to release!")
		return
	end

	-- Create confirmation dialog
	PlotUI.CreateConfirmationDialog(
		"🗑️ Release Plot " .. playerPlotInfo.PlotNumber,
		"Are you sure you want to release this plot?\n\nThis will remove all your buildings and cannot be undone!",
		function()
			-- Confirmed - release the plot
			RemoteEvents.ReleasePlot:FireServer()

			-- Show notification
			RemoteEvents.ShowNotification:FireClient(player, "Success", "Plot " .. playerPlotInfo.PlotNumber .. " has been released!")

			-- Close window and refresh after a delay
			task.wait(1)
			PlotUI.RefreshPlotInfo()
		end,
		function()
			-- Cancelled - do nothing
			print("🏘️ Plot release cancelled")
		end
	)
end

-- Create confirmation dialog
function PlotUI.CreateConfirmationDialog(title, message, onConfirm, onCancel)
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end

	-- Create overlay
	local overlay = Instance.new("Frame")
	overlay.Name = "ConfirmationOverlay"
	overlay.Size = UDim2.new(1, 0, 1, 0)
	overlay.Position = UDim2.new(0, 0, 0, 0)
	overlay.BackgroundColor3 = Color3.new(0, 0, 0)
	overlay.BackgroundTransparency = 0.5
	overlay.BorderSizePixel = 0
	overlay.ZIndex = 20
	overlay.Parent = screenGui

	-- Create dialog
	local dialog = Instance.new("Frame")
	dialog.Name = "ConfirmationDialog"
	dialog.Size = UDim2.new(0, 350, 0, 200)
	dialog.Position = UDim2.new(0.5, -175, 0.5, -100)
	dialog.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	dialog.BorderSizePixel = 0
	dialog.ZIndex = 21
	dialog.Parent = overlay

	local dialogCorner = Instance.new("UICorner")
	dialogCorner.CornerRadius = UDim.new(0, 12)
	dialogCorner.Parent = dialog

	-- Title
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Size = UDim2.new(1, -20, 0, 40)
	titleLabel.Position = UDim2.new(0, 10, 0, 10)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Text = title
	titleLabel.TextColor3 = Color3.new(1, 1, 1)
	titleLabel.TextSize = 18
	titleLabel.Font = Enum.Font.SourceSansBold
	titleLabel.TextXAlignment = Enum.TextXAlignment.Left
	titleLabel.Parent = dialog

	-- Message
	local messageLabel = Instance.new("TextLabel")
	messageLabel.Size = UDim2.new(1, -20, 0, 80)
	messageLabel.Position = UDim2.new(0, 10, 0, 50)
	messageLabel.BackgroundTransparency = 1
	messageLabel.Text = message
	messageLabel.TextColor3 = Color3.new(0.9, 0.9, 0.9)
	messageLabel.TextSize = 14
	messageLabel.Font = Enum.Font.SourceSans
	messageLabel.TextXAlignment = Enum.TextXAlignment.Left
	messageLabel.TextYAlignment = Enum.TextYAlignment.Top
	messageLabel.TextWrapped = true
	messageLabel.Parent = dialog

	-- Confirm button
	local confirmButton = Instance.new("TextButton")
	confirmButton.Size = UDim2.new(0, 100, 0, 35)
	confirmButton.Position = UDim2.new(1, -220, 1, -50)
	confirmButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
	confirmButton.Text = "✓ Confirm"
	confirmButton.TextColor3 = Color3.new(1, 1, 1)
	confirmButton.TextSize = 14
	confirmButton.Font = Enum.Font.SourceSansBold
	confirmButton.Parent = dialog

	local confirmCorner = Instance.new("UICorner")
	confirmCorner.CornerRadius = UDim.new(0, 6)
	confirmCorner.Parent = confirmButton

	-- Cancel button
	local cancelButton = Instance.new("TextButton")
	cancelButton.Size = UDim2.new(0, 100, 0, 35)
	cancelButton.Position = UDim2.new(1, -110, 1, -50)
	cancelButton.BackgroundColor3 = Color3.new(0.4, 0.4, 0.4)
	cancelButton.Text = "✗ Cancel"
	cancelButton.TextColor3 = Color3.new(1, 1, 1)
	cancelButton.TextSize = 14
	cancelButton.Font = Enum.Font.SourceSansBold
	cancelButton.Parent = dialog

	local cancelCorner = Instance.new("UICorner")
	cancelCorner.CornerRadius = UDim.new(0, 6)
	cancelCorner.Parent = cancelButton

	-- Button functionality
	confirmButton.MouseButton1Click:Connect(function()
		overlay:Destroy()
		if onConfirm then onConfirm() end
	end)

	cancelButton.MouseButton1Click:Connect(function()
		overlay:Destroy()
		if onCancel then onCancel() end
	end)

	-- Click overlay to cancel
	overlay.MouseButton1Click:Connect(function()
		overlay:Destroy()
		if onCancel then onCancel() end
	end)

	-- Animate dialog in
	dialog.Size = UDim2.new(0, 0, 0, 0)
	dialog.Position = UDim2.new(0.5, 0, 0.5, 0)

	TweenService:Create(dialog, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
		Size = UDim2.new(0, 350, 0, 200),
		Position = UDim2.new(0.5, -175, 0.5, -100)
	}):Play()
end

-- Open plot browser
function PlotUI.OpenPlotBrowser()
	print("🔍 Opening plot browser...")

	-- Get all plots info
	local success, plotsInfo = pcall(function()
		return RemoteFunctions.GetAllPlotsInfo:InvokeServer()
	end)

	if success and plotsInfo then
		PlotUI.CreatePlotBrowser(plotsInfo)
	else
		warn("🔍 Failed to get plots info:", plotsInfo)
		RemoteEvents.ShowNotification:FireClient(player, "Error", "Failed to load plots information!")
	end
end

-- Create plot browser window
function PlotUI.CreatePlotBrowser(plotsInfo)
	if plotBrowserWindow then
		plotBrowserWindow:Destroy()
	end

	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then
		warn("🔍 UrbanSimUI not found!")
		return
	end

	-- Main browser window with mobile responsiveness
	plotBrowserWindow = Instance.new("Frame")
	plotBrowserWindow.Name = "PlotBrowserWindow"

	-- Responsive sizing for mobile
	local screenSize = workspace.CurrentCamera.ViewportSize
	local isMobile = screenSize.X < 800 or screenSize.Y < 600

	if isMobile then
		plotBrowserWindow.Size = UDim2.new(0.95, 0, 0.8, 0)
		plotBrowserWindow.Position = UDim2.new(0.025, 0, 0.1, 0)
	else
		plotBrowserWindow.Size = UDim2.new(0, 600, 0, 400)
		plotBrowserWindow.Position = UDim2.new(0.5, -300, 0.5, -200)
	end

	plotBrowserWindow.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	plotBrowserWindow.BackgroundTransparency = 0 -- Ensure visibility
	plotBrowserWindow.BorderSizePixel = 0
	plotBrowserWindow.Visible = true
	plotBrowserWindow.ZIndex = 15 -- Above plot window
	plotBrowserWindow.Parent = screenGui

	-- Add corner radius
	local windowCorner = Instance.new("UICorner")
	windowCorner.CornerRadius = UDim.new(0, 12)
	windowCorner.Parent = plotBrowserWindow

	-- Title bar
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 50)
	titleBar.Position = UDim2.new(0, 0, 0, 0)
	titleBar.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	titleBar.BorderSizePixel = 0
	titleBar.Parent = plotBrowserWindow

	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 12)
	titleCorner.Parent = titleBar

	-- Title text
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -100, 1, 0)
	titleText.Position = UDim2.new(0, 15, 0, 0)
	titleText.BackgroundTransparency = 1
	titleText.Text = "🔍 Browse Plots"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextSize = 20
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextXAlignment = Enum.TextXAlignment.Left
	titleText.Parent = titleBar

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -40, 0, 10)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextSize = 18
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = titleBar

	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton

	-- Plots grid
	local plotsFrame = Instance.new("ScrollingFrame")
	plotsFrame.Name = "PlotsFrame"
	plotsFrame.Size = UDim2.new(1, -20, 1, -70)
	plotsFrame.Position = UDim2.new(0, 10, 0, 60)
	plotsFrame.BackgroundColor3 = Color3.new(0.12, 0.12, 0.17)
	plotsFrame.BorderSizePixel = 0
	plotsFrame.ScrollBarThickness = 8
	plotsFrame.Parent = plotBrowserWindow

	local plotsCorner = Instance.new("UICorner")
	plotsCorner.CornerRadius = UDim.new(0, 8)
	plotsCorner.Parent = plotsFrame

	-- Grid layout
	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellSize = UDim2.new(0, 180, 0, 120)
	gridLayout.CellPadding = UDim2.new(0, 10, 0, 10)
	gridLayout.SortOrder = Enum.SortOrder.Name
	gridLayout.Parent = plotsFrame

	-- Add padding
	local padding = Instance.new("UIPadding")
	padding.PaddingAll = UDim.new(0, 10)
	padding.Parent = plotsFrame

	-- Create plot cards
	for plotNumber = 1, 7 do
		local plotInfo = plotsInfo[plotNumber]
		if plotInfo then
			PlotUI.CreatePlotCard(plotsFrame, plotInfo)
		end
	end

	-- Update canvas size
	gridLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
		plotsFrame.CanvasSize = UDim2.new(0, 0, 0, gridLayout.AbsoluteContentSize.Y + 20)
	end)

	-- Close button functionality
	closeButton.MouseButton1Click:Connect(function()
		plotBrowserWindow:Destroy()
		plotBrowserWindow = nil
	end)

	print("🔍 Plot browser created!")
end

-- Create individual plot card
function PlotUI.CreatePlotCard(parent, plotInfo)
	local plotCard = Instance.new("Frame")
	plotCard.Name = "PlotCard" .. plotInfo.PlotNumber
	plotCard.BackgroundColor3 = plotInfo.IsAvailable and Color3.new(0.2, 0.4, 0.2) or Color3.new(0.3, 0.3, 0.4)
	plotCard.BorderSizePixel = 0
	plotCard.Parent = parent

	local cardCorner = Instance.new("UICorner")
	cardCorner.CornerRadius = UDim.new(0, 8)
	cardCorner.Parent = plotCard

	-- Plot number
	local plotNumberLabel = Instance.new("TextLabel")
	plotNumberLabel.Size = UDim2.new(1, 0, 0, 25)
	plotNumberLabel.Position = UDim2.new(0, 0, 0, 5)
	plotNumberLabel.BackgroundTransparency = 1
	plotNumberLabel.Text = "🏘️ Plot " .. plotInfo.PlotNumber
	plotNumberLabel.TextColor3 = Color3.new(1, 1, 1)
	plotNumberLabel.TextSize = 14
	plotNumberLabel.Font = Enum.Font.SourceSansBold
	plotNumberLabel.Parent = plotCard

	-- Owner info
	local ownerLabel = Instance.new("TextLabel")
	ownerLabel.Size = UDim2.new(1, 0, 0, 20)
	ownerLabel.Position = UDim2.new(0, 0, 0, 30)
	ownerLabel.BackgroundTransparency = 1
	ownerLabel.Text = plotInfo.IsAvailable and "👤 Available" or ("👤 " .. (plotInfo.OwnerName or "Unknown"))
	ownerLabel.TextColor3 = plotInfo.IsAvailable and Color3.new(0.8, 1, 0.8) or Color3.new(0.8, 0.8, 0.8)
	ownerLabel.TextSize = 12
	ownerLabel.Font = Enum.Font.SourceSans
	ownerLabel.Parent = plotCard

	-- Buildings count
	local buildingsLabel = Instance.new("TextLabel")
	buildingsLabel.Size = UDim2.new(1, 0, 0, 20)
	buildingsLabel.Position = UDim2.new(0, 0, 0, 50)
	buildingsLabel.BackgroundTransparency = 1
	buildingsLabel.Text = "🏗️ " .. plotInfo.Buildings .. " Buildings"
	buildingsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	buildingsLabel.TextSize = 12
	buildingsLabel.Font = Enum.Font.SourceSans
	buildingsLabel.Parent = plotCard

	-- Action button
	local actionButton = Instance.new("TextButton")
	actionButton.Size = UDim2.new(1, -10, 0, 25)
	actionButton.Position = UDim2.new(0, 5, 0, 75)
	actionButton.BackgroundColor3 = plotInfo.IsAvailable and Color3.new(0.2, 0.6, 0.8) or Color3.new(0.5, 0.5, 0.5)
	actionButton.Text = plotInfo.IsAvailable and "🏘️ Claim Plot" or "📍 View Plot"
	actionButton.TextColor3 = Color3.new(1, 1, 1)
	actionButton.TextSize = 12
	actionButton.Font = Enum.Font.SourceSansBold
	actionButton.Parent = plotCard

	local actionCorner = Instance.new("UICorner")
	actionCorner.CornerRadius = UDim.new(0, 6)
	actionCorner.Parent = actionButton

	-- Button functionality
	actionButton.MouseButton1Click:Connect(function()
		if plotInfo.IsAvailable then
			-- Claim the plot
			RemoteEvents.ClaimPlot:FireServer(plotInfo.PlotNumber)

			-- Close browser
			if plotBrowserWindow then
				plotBrowserWindow:Destroy()
				plotBrowserWindow = nil
			end
		else
			-- View/teleport to plot (if owned by player)
			if plotInfo.Owner == player.UserId then
				RemoteEvents.TeleportToPlot:FireServer(plotInfo.PlotNumber)

				-- Close browser
				if plotBrowserWindow then
					plotBrowserWindow:Destroy()
					plotBrowserWindow = nil
				end
			else
				RemoteEvents.ShowNotification:FireClient(player, "Info", "This plot is owned by " .. (plotInfo.OwnerName or "someone else") .. ".")
			end
		end
	end)

	-- Hover effect
	actionButton.MouseEnter:Connect(function()
		TweenService:Create(actionButton, TweenInfo.new(0.2), {
			BackgroundColor3 = plotInfo.IsAvailable and Color3.new(0.3, 0.7, 0.9) or Color3.new(0.6, 0.6, 0.6)
		}):Play()
	end)

	actionButton.MouseLeave:Connect(function()
		TweenService:Create(actionButton, TweenInfo.new(0.2), {
			BackgroundColor3 = plotInfo.IsAvailable and Color3.new(0.2, 0.6, 0.8) or Color3.new(0.5, 0.5, 0.5)
		}):Play()
	end)
end

-- Create plot button in main UI
function PlotUI.CreatePlotButton()
	print("🏘️ Creating plot button...")

	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then
		warn("🏘️ UrbanSimUI not found! Retrying in 2 seconds...")
		task.wait(2)
		screenGui = playerGui:FindFirstChild("UrbanSimUI")
		if not screenGui then
			warn("🏘️ UrbanSimUI still not found after retry!")
			return
		end
	end

	local mainFrame = screenGui:FindFirstChild("MainFrame")
	if not mainFrame then
		warn("🏘️ MainFrame not found!")
		return
	end

	local topBar = mainFrame:FindFirstChild("TopBar")
	if not topBar then
		warn("🏘️ TopBar not found!")
		return
	end

	-- Check if plot button already exists
	local existingButton = topBar:FindFirstChild("PlotButton")
	if existingButton then
		print("🏘️ Plot button already exists, skipping creation...")
		return
	end

	-- Create plot button - positioned next to crafting button with proper spacing
	local plotButton = Instance.new("TextButton")
	plotButton.Name = "PlotButton"
	plotButton.Size = UDim2.new(0, 100, 0, 40)
	plotButton.Position = UDim2.new(1, -440, 0, 10) -- Next to crafting button (-330 - 100 - 10 margin)
	plotButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
	plotButton.BackgroundTransparency = 0 -- Ensure visibility
	plotButton.Text = "🏘️ Plot"
	plotButton.TextColor3 = Color3.new(1, 1, 1)
	plotButton.TextScaled = true
	plotButton.Font = Enum.Font.SourceSansBold
	plotButton.ZIndex = 5 -- Ensure it's visible
	plotButton.Parent = topBar

	-- Add corner radius
	local plotCorner = Instance.new("UICorner")
	plotCorner.CornerRadius = UDim.new(0, 6)
	plotCorner.Parent = plotButton

	-- Button functionality
	plotButton.MouseButton1Click:Connect(function()
		if isOpen then
			PlotUI.ClosePlotWindow()
		else
			PlotUI.OpenPlotWindow()
		end
	end)

	-- Hover effect
	plotButton.MouseEnter:Connect(function()
		TweenService:Create(plotButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.3, 0.7, 0.9)
		}):Play()
	end)

	plotButton.MouseLeave:Connect(function()
		TweenService:Create(plotButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.2, 0.6, 0.8)
		}):Play()
	end)

	print("🏘️ Plot button created!")
end

-- Track initialization state
local isInitialized = false

-- Initialize plot UI
function PlotUI.Initialize()
	if isInitialized then
		print("🏘️ Plot UI already initialized, skipping...")
		return
	end

	print("🏘️ Initializing Plot UI...")
	isInitialized = true

	-- Wait for main UI to load
	task.wait(2)

	PlotUI.CreatePlotButton()

	-- Setup event listeners
	RemoteEvents.PlotClaimed.OnClientEvent:Connect(function(plotNumber, playerName)
		print("🏘️ Plot", plotNumber, "was claimed by", playerName)
		-- Refresh plot info if we have the window open
		if isOpen and plotWindow then
			PlotUI.RefreshPlotInfo()
		end
	end)

	RemoteEvents.PlotReleased.OnClientEvent:Connect(function(plotNumber, playerName)
		print("🏘️ Plot", plotNumber, "was released by", playerName)
		-- Refresh plot info if we have the window open
		if isOpen and plotWindow then
			PlotUI.RefreshPlotInfo()
		end
	end)

	print("✅ Plot UI initialized!")
end

-- Auto-initialize (will be prevented if called again from init.client)
PlotUI.Initialize()

return PlotUI
