--[[
	Advanced Crafting System
	Handles crafting recipes, queues, and production chains
]]

local Config = require(script.Parent.Config)

local CraftingSystem = {}

-- Crafting queue management
function CraftingSystem.AddToQueue(playerData, recipe, quantity, slotId)
	quantity = quantity or 1
	slotId = slotId or CraftingSystem.GetNextAvailableSlot(playerData)
	
	if not slotId then
		return false, "No available crafting slots"
	end
	
	local recipeConfig = Config.CRAFTING_RECIPES[recipe]
	if not recipeConfig then
		return false, "Invalid recipe"
	end
	
	-- Check ingredients
	local totalRequired = {}
	for ingredient, amount in pairs(recipeConfig.Ingredients) do
		totalRequired[ingredient] = amount * quantity
	end
	
	for ingredient, required in pairs(totalRequired) do
		local available = playerData.Resources[ingredient] or 0
		if available < required then
			return false, "Insufficient " .. ingredient
		end
	end
	
	-- Create crafting job
	local craftingJob = {
		Id = tostring(tick()) .. "_" .. recipe,
		Recipe = recipe,
		Quantity = quantity,
		StartTime = tick(),
		Duration = recipeConfig.Time * quantity,
		SlotId = slotId,
		Progress = 0,
		Status = "Active"
	}
	
	-- Add to queue
	if not playerData.CraftingQueue then
		playerData.CraftingQueue = {}
	end
	
	playerData.CraftingQueue[slotId] = craftingJob
	
	-- Consume ingredients
	for ingredient, required in pairs(totalRequired) do
		playerData.Resources[ingredient] = playerData.Resources[ingredient] - required
	end
	
	return true, "Crafting started", craftingJob
end

-- Get next available crafting slot
function CraftingSystem.GetNextAvailableSlot(playerData)
	local maxSlots = CraftingSystem.GetMaxCraftingSlots(playerData)
	
	for i = 1, maxSlots do
		if not playerData.CraftingQueue or not playerData.CraftingQueue[i] then
			return i
		end
	end
	
	return nil -- No available slots
end

-- Get maximum crafting slots for player
function CraftingSystem.GetMaxCraftingSlots(playerData)
	local baseSlots = 2
	local bonusSlots = 0
	
	-- Check for gamepass bonuses
	-- This would be implemented with actual gamepass checking
	-- if hasGamepass(QUEUE_PRO) then bonusSlots = bonusSlots + 2 end
	
	return baseSlots + bonusSlots
end

-- Update crafting progress
function CraftingSystem.UpdateCraftingProgress(playerData)
	if not playerData.CraftingQueue then
		return {}
	end
	
	local currentTime = tick()
	local completedJobs = {}
	
	for slotId, job in pairs(playerData.CraftingQueue) do
		if job.Status == "Active" then
			local elapsed = currentTime - job.StartTime
			job.Progress = math.min(elapsed / job.Duration, 1)
			
			if job.Progress >= 1 then
				-- Job completed
				job.Status = "Completed"
				table.insert(completedJobs, job)
			end
		end
	end
	
	return completedJobs
end

-- Complete crafting job
function CraftingSystem.CompleteCraftingJob(playerData, slotId)
	local job = playerData.CraftingQueue and playerData.CraftingQueue[slotId]
	if not job or job.Status ~= "Completed" then
		return false, "No completed job in this slot"
	end
	
	local recipeConfig = Config.CRAFTING_RECIPES[job.Recipe]
	if not recipeConfig then
		return false, "Invalid recipe"
	end
	
	-- Add crafted items to inventory
	local outputAmount = recipeConfig.Output * job.Quantity
	playerData.Resources[job.Recipe] = (playerData.Resources[job.Recipe] or 0) + outputAmount
	
	-- Remove job from queue
	playerData.CraftingQueue[slotId] = nil
	
	return true, "Crafting completed", {
		Recipe = job.Recipe,
		Quantity = outputAmount
	}
end

-- Cancel crafting job
function CraftingSystem.CancelCraftingJob(playerData, slotId)
	local job = playerData.CraftingQueue and playerData.CraftingQueue[slotId]
	if not job then
		return false, "No job in this slot"
	end
	
	if job.Status == "Completed" then
		return false, "Cannot cancel completed job"
	end
	
	-- Refund ingredients (partial refund based on progress)
	local recipeConfig = Config.CRAFTING_RECIPES[job.Recipe]
	if recipeConfig then
		local refundRate = math.max(0.5, 1 - job.Progress) -- Minimum 50% refund
		
		for ingredient, amount in pairs(recipeConfig.Ingredients) do
			local refundAmount = math.floor(amount * job.Quantity * refundRate)
			playerData.Resources[ingredient] = (playerData.Resources[ingredient] or 0) + refundAmount
		end
	end
	
	-- Remove job from queue
	playerData.CraftingQueue[slotId] = nil
	
	return true, "Crafting cancelled"
end

-- Speed up crafting with premium currency
function CraftingSystem.SpeedUpCrafting(playerData, slotId, cashCost)
	local job = playerData.CraftingQueue and playerData.CraftingQueue[slotId]
	if not job or job.Status ~= "Active" then
		return false, "No active job in this slot"
	end
	
	-- Check if player has enough cash
	if (playerData.Cash or 0) < cashCost then
		return false, "Insufficient Cash"
	end
	
	-- Complete the job instantly
	job.Progress = 1
	job.Status = "Completed"
	playerData.Cash = playerData.Cash - cashCost
	
	return true, "Crafting completed instantly"
end

-- Get crafting time remaining
function CraftingSystem.GetTimeRemaining(job)
	if not job or job.Status ~= "Active" then
		return 0
	end
	
	local elapsed = tick() - job.StartTime
	local remaining = math.max(0, job.Duration - elapsed)
	return remaining
end

-- Format time for display
function CraftingSystem.FormatTime(seconds)
	if seconds <= 0 then
		return "00:00"
	end
	
	local minutes = math.floor(seconds / 60)
	local secs = math.floor(seconds % 60)
	
	return string.format("%02d:%02d", minutes, secs)
end

-- Get all available recipes for player
function CraftingSystem.GetAvailableRecipes(playerData)
	local availableRecipes = {}
	
	for recipe, config in pairs(Config.CRAFTING_RECIPES) do
		-- Check if player has unlocked this recipe
		-- For now, all recipes are available
		local canCraft = true
		
		-- Check if player has any ingredients
		local hasIngredients = false
		for ingredient, _ in pairs(config.Ingredients) do
			if (playerData.Resources[ingredient] or 0) > 0 then
				hasIngredients = true
				break
			end
		end
		
		if canCraft then
			table.insert(availableRecipes, {
				Recipe = recipe,
				Config = config,
				HasIngredients = hasIngredients
			})
		end
	end
	
	return availableRecipes
end

-- Calculate maximum craftable quantity
function CraftingSystem.GetMaxCraftableQuantity(playerData, recipe)
	local recipeConfig = Config.CRAFTING_RECIPES[recipe]
	if not recipeConfig then
		return 0
	end
	
	local maxQuantity = math.huge
	
	for ingredient, required in pairs(recipeConfig.Ingredients) do
		local available = playerData.Resources[ingredient] or 0
		local possibleQuantity = math.floor(available / required)
		maxQuantity = math.min(maxQuantity, possibleQuantity)
	end
	
	return maxQuantity == math.huge and 0 or maxQuantity
end

-- Get crafting efficiency (for future upgrades)
function CraftingSystem.GetCraftingEfficiency(playerData)
	local baseEfficiency = 1.0
	local bonusEfficiency = 0.0
	
	-- Check for building bonuses, research, etc.
	-- This would be expanded with actual bonus calculations
	
	return baseEfficiency + bonusEfficiency
end

return CraftingSystem
