-- TestStatisticsFrame.client.luau
-- Test script to verify StatisticsFrame layout is working correctly

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for the main UI to be created
local screenGui = playerGui:WaitForChild("UrbanSimUI", 10)
if not screenGui then
	warn("🧪 UrbanSimUI not found - cannot test StatisticsFrame")
	return
end

-- Wait for StatisticsUI module to be available
local success, StatisticsUI = pcall(function()
	return require(script.Parent:WaitForChild("StatisticsUI"))
end)

if not success then
	warn("🧪 Failed to load StatisticsUI module:", StatisticsUI)
	return
end

print("🧪 Starting StatisticsFrame test...")

-- Test 1: Initialize StatisticsUI
print("🧪 Test 1: Initializing StatisticsUI...")
StatisticsUI.Initialize()
task.wait(2)

-- Test 2: Open Statistics Frame
print("🧪 Test 2: Opening Statistics Frame...")
StatisticsUI.OpenStatistics()
task.wait(2)

-- Test 3: Debug Layout
print("🧪 Test 3: Debugging Layout...")
StatisticsUI.DebugLayout()
task.wait(2)

-- Test 4: Close Statistics Frame
print("🧪 Test 4: Closing Statistics Frame...")
StatisticsUI.CloseStatistics()
task.wait(2)

-- Test 5: Toggle Statistics Frame
print("🧪 Test 5: Toggling Statistics Frame...")
StatisticsUI.ToggleStatistics()
task.wait(2)
StatisticsUI.ToggleStatistics()

print("🧪 StatisticsFrame test completed!")

-- Create a simple GUI to manually test
local testGui = Instance.new("ScreenGui")
testGui.Name = "StatisticsFrameTest"
testGui.Parent = playerGui

local testButton = Instance.new("TextButton")
testButton.Name = "TestButton"
testButton.Size = UDim2.new(0, 200, 0, 50)
testButton.Position = UDim2.new(0, 10, 0, 100)
testButton.BackgroundColor3 = Color3.new(0, 0.8, 0)
testButton.Text = "🧪 Test Statistics Frame"
testButton.TextColor3 = Color3.new(1, 1, 1)
testButton.TextScaled = true
testButton.Font = Enum.Font.SourceSansBold
testButton.Parent = testGui

local corner = Instance.new("UICorner")
corner.CornerRadius = UDim.new(0, 8)
corner.Parent = testButton

testButton.MouseButton1Click:Connect(function()
	StatisticsUI.TestStatistics()
end)

print("🧪 Test button created! Click it to test StatisticsFrame manually.")
