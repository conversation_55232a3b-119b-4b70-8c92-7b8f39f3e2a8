--[[
	Daily Rewards System
	Handles daily login rewards, streaks, and bonus collections
]]

local DailyRewards = {}

-- Reward configurations
DailyRewards.REWARDS = {
	-- Day 1-7 (First Week)
	[1] = {Pieces = 500, XP = 50, Type = "Basic"},
	[2] = {Pieces = 750, XP = 75, Type = "Basic"},
	[3] = {Pieces = 1000, XP = 100, Metal = 5, Type = "Enhanced"},
	[4] = {Pieces = 1250, XP = 125, Plastic = 5, Type = "Enhanced"},
	[5] = {Pieces = 1500, XP = 150, Cles = 1, Type = "Premium"},
	[6] = {Pieces = 2000, XP = 200, Metal = 10, Plastic = 10, Type = "Premium"},
	[7] = {Pieces = 3000, XP = 300, Cles = 2, Cash = 5, Type = "Legendary"},
	
	-- Day 8-14 (Second Week)
	[8] = {Pieces = 1000, XP = 100, Metal = 8, Type = "Enhanced"},
	[9] = {Pieces = 1500, XP = 150, Plastic = 8, Type = "Enhanced"},
	[10] = {Pieces = 2000, XP = 200, Cles = 1, Metal = 5, Type = "Premium"},
	[11] = {Pieces = 2500, XP = 250, Wood = 10, Plastic = 10, Type = "Premium"},
	[12] = {Pieces = 3000, XP = 300, Cles = 2, CarteMere = 1, Type = "Premium"},
	[13] = {Pieces = 4000, XP = 400, Cash = 10, Metal = 15, Type = "Legendary"},
	[14] = {Pieces = 5000, XP = 500, Cles = 3, Cash = 15, ClesDiamant = 1, Type = "Epic"},
	
	-- Day 15-21 (Third Week)
	[15] = {Pieces = 2000, XP = 200, CarteMere = 2, Type = "Enhanced"},
	[16] = {Pieces = 3000, XP = 300, Metre = 1, Type = "Premium"},
	[17] = {Pieces = 4000, XP = 400, PC = 1, Type = "Premium"},
	[18] = {Pieces = 5000, XP = 500, Cash = 20, Cles = 2, Type = "Legendary"},
	[19] = {Pieces = 6000, XP = 600, ClesDiamant = 1, PC = 2, Type = "Epic"},
	[20] = {Pieces = 8000, XP = 800, Cash = 25, GammaCoin = 1, Type = "Epic"},
	[21] = {Pieces = 10000, XP = 1000, ClesDiamant = 2, GammaCoin = 2, Cash = 50, Type = "Mythic"},
	
	-- Day 22-28 (Fourth Week)
	[22] = {Pieces = 3000, XP = 300, PC = 2, Type = "Premium"},
	[23] = {Pieces = 4000, XP = 400, Cash = 15, CarteMere = 3, Type = "Legendary"},
	[24] = {Pieces = 5000, XP = 500, ClesDiamant = 1, Metre = 2, Type = "Epic"},
	[25] = {Pieces = 7000, XP = 700, GammaCoin = 1, PC = 3, Type = "Epic"},
	[26] = {Pieces = 9000, XP = 900, Cash = 40, ClesDiamant = 2, Type = "Mythic"},
	[27] = {Pieces = 12000, XP = 1200, GammaCoin = 3, Cash = 60, Type = "Mythic"},
	[28] = {Pieces = 15000, XP = 1500, GammaCoin = 5, ClesDiamant = 3, Cash = 100, Type = "Ultimate"}
}

-- Minute-based bonus rewards
DailyRewards.MINUTE_REWARDS = {
	-- Every 5 minutes
	[5] = {Pieces = 50, XP = 5},
	[10] = {Pieces = 100, XP = 10},
	[15] = {Pieces = 150, XP = 15, Metal = 1},
	[20] = {Pieces = 200, XP = 20},
	[25] = {Pieces = 250, XP = 25},
	[30] = {Pieces = 300, XP = 30, Plastic = 1},
	[45] = {Pieces = 450, XP = 45, Cles = 1},
	[60] = {Pieces = 600, XP = 60, Metal = 2, Plastic = 2} -- Hourly bonus
}

-- Reward type colors
DailyRewards.REWARD_COLORS = {
	Basic = Color3.new(0.7, 0.7, 0.7),      -- Gray
	Enhanced = Color3.new(0.2, 0.8, 0.2),   -- Green
	Premium = Color3.new(0.2, 0.6, 1),      -- Blue
	Legendary = Color3.new(1, 0.8, 0.2),    -- Gold
	Epic = Color3.new(0.8, 0.2, 1),         -- Purple
	Mythic = Color3.new(1, 0.2, 0.2),       -- Red
	Ultimate = Color3.new(1, 0.4, 0.8)      -- Pink
}

-- Calculate current day in streak
function DailyRewards.GetCurrentDay(playerData)
	local lastLogin = playerData.LastDailyReward or 0
	local currentTime = os.time()
	local daysSinceEpoch = math.floor(currentTime / 86400) -- 86400 seconds in a day
	local lastLoginDay = math.floor(lastLogin / 86400)
	
	if daysSinceEpoch == lastLoginDay then
		-- Already claimed today
		return playerData.DailyStreak or 1, false
	elseif daysSinceEpoch == lastLoginDay + 1 then
		-- Next day, continue streak
		local newStreak = (playerData.DailyStreak or 0) + 1
		return newStreak, true
	else
		-- Streak broken, reset to day 1
		return 1, true
	end
end

-- Get reward for specific day
function DailyRewards.GetDayReward(day)
	-- Cycle through 28-day calendar
	local rewardDay = ((day - 1) % 28) + 1
	return DailyRewards.REWARDS[rewardDay]
end

-- Check if player can claim daily reward
function DailyRewards.CanClaimDaily(playerData)
	local currentDay, canClaim = DailyRewards.GetCurrentDay(playerData)
	return canClaim, currentDay
end

-- Check minute-based rewards
function DailyRewards.CheckMinuteRewards(playerData)
	local currentTime = os.time()
	local lastMinuteReward = playerData.LastMinuteReward or currentTime
	local minutesPassed = math.floor((currentTime - lastMinuteReward) / 60)
	
	local availableRewards = {}
	
	-- Check each minute milestone
	for minutes, reward in pairs(DailyRewards.MINUTE_REWARDS) do
		if minutesPassed >= minutes then
			-- Check if this reward was already claimed recently
			local lastClaimed = playerData.MinuteRewardsClaimed and playerData.MinuteRewardsClaimed[minutes] or 0
			local timeSinceLastClaim = currentTime - lastClaimed
			
			-- Can claim if enough time has passed (prevent spam)
			if timeSinceLastClaim >= minutes * 60 then
				table.insert(availableRewards, {
					Minutes = minutes,
					Reward = reward,
					Type = minutes >= 60 and "Hourly" or "Minute"
				})
			end
		end
	end
	
	return availableRewards
end

-- Apply streak multiplier
function DailyRewards.ApplyStreakMultiplier(reward, streak)
	local multiplier = 1
	
	-- Streak bonuses
	if streak >= 7 then multiplier = multiplier + 0.25 end   -- +25% after 1 week
	if streak >= 14 then multiplier = multiplier + 0.25 end  -- +50% after 2 weeks
	if streak >= 21 then multiplier = multiplier + 0.25 end  -- +75% after 3 weeks
	if streak >= 28 then multiplier = multiplier + 0.5 end   -- +125% after 4 weeks
	
	-- Apply multiplier to numeric rewards
	local multipliedReward = {}
	for key, value in pairs(reward) do
		if type(value) == "number" and key ~= "Type" then
			multipliedReward[key] = math.floor(value * multiplier)
		else
			multipliedReward[key] = value
		end
	end
	
	return multipliedReward, multiplier
end

-- Format reward text for display
function DailyRewards.FormatReward(reward)
	local parts = {}
	
	-- Order rewards by importance
	local order = {"Pieces", "Cash", "XP", "GammaCoin", "ClesDiamant", "Cles", "PC", "CarteMere", "Metre", "Metal", "Plastic", "Wood"}
	
	for _, currency in ipairs(order) do
		if reward[currency] and reward[currency] > 0 then
			local icon = DailyRewards.GetCurrencyIcon(currency)
			table.insert(parts, icon .. " " .. reward[currency])
		end
	end
	
	return table.concat(parts, " • ")
end

-- Get currency icon
function DailyRewards.GetCurrencyIcon(currency)
	local icons = {
		Pieces = "💰",
		Cash = "💎",
		XP = "⭐",
		GammaCoin = "🪙",
		ClesDiamant = "💠",
		Cles = "🔑",
		PC = "💻",
		CarteMere = "🔧",
		Metre = "📏",
		Metal = "⚙️",
		Plastic = "🧪",
		Wood = "🪵"
	}
	return icons[currency] or "📦"
end

-- Get days until next tier
function DailyRewards.GetDaysUntilNextTier(currentDay)
	local tiers = {7, 14, 21, 28}
	for _, tier in ipairs(tiers) do
		if currentDay < tier then
			return tier - currentDay, tier
		end
	end
	return 0, 28 -- Already at max tier
end

-- Calculate total login time bonus
function DailyRewards.GetLoginTimeBonus(playerData)
	local sessionStart = playerData.SessionStartTime or os.time()
	local currentTime = os.time()
	local sessionMinutes = math.floor((currentTime - sessionStart) / 60)
	
	-- Bonus for staying online
	local bonus = math.floor(sessionMinutes / 10) * 50 -- 50 pieces per 10 minutes
	return math.min(bonus, 1000) -- Cap at 1000 pieces
end

return DailyRewards
